// User types
export interface User {
	id: string;
	email: string;
	name: string;
	role: 'ADMIN' | 'USER';
	avatar?: string;
	createdAt: Date;
	updatedAt: Date;
}

// Product types
export interface Product {
	id: string;
	name: string;
	description: string;
	price: number;
	salePrice?: number;
	images: string[];
	category: Category;
	categoryId: string;
	stock: number;
	sku: string;
	slug: string;
	featured: boolean;
	status: 'ACTIVE' | 'INACTIVE' | 'OUT_OF_STOCK';
	tags: string[];
	createdAt: Date;
	updatedAt: Date;
}

// Category types
export interface Category {
	id: string;
	name: string;
	description?: string;
	slug: string;
	image?: string;
	parentId?: string;
	parent?: Category;
	children?: Category[];
	products?: Product[];
	createdAt: Date;
	updatedAt: Date;
}

// Cart types
export interface CartItem {
	id: string;
	productId: string;
	product: Product;
	quantity: number;
	price: number;
}

export interface Cart {
	id: string;
	userId?: string;
	items: CartItem[];
	total: number;
	createdAt: Date;
	updatedAt: Date;
}

// Order types
export interface Order {
	id: string;
	userId: string;
	user: User;
	items: OrderItem[];
	total: number;
	status: 'PENDING' | 'CONFIRMED' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED';
	shippingAddress: Address;
	billingAddress?: Address;
	paymentMethod: 'COD' | 'BANK_TRANSFER' | 'CREDIT_CARD';
	paymentStatus: 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED';
	notes?: string;
	createdAt: Date;
	updatedAt: Date;
}

export interface OrderItem {
	id: string;
	orderId: string;
	productId: string;
	product: Product;
	quantity: number;
	price: number;
	total: number;
}

// Address types
export interface Address {
	id: string;
	fullName: string;
	phone: string;
	address: string;
	ward: string;
	district: string;
	province: string;
	isDefault: boolean;
}

// API Response types
export interface ApiResponse<T = any> {
	success: boolean;
	data?: T;
	message?: string;
	error?: string;
}

export interface PaginatedResponse<T> {
	data: T[];
	total: number;
	page: number;
	limit: number;
	totalPages: number;
}

// Filter and search types
export interface ProductFilters {
	categoryId?: string;
	minPrice?: number;
	maxPrice?: number;
	tags?: string[];
	featured?: boolean;
	status?: Product['status'];
	search?: string;
}

export interface PaginationParams {
	page?: number;
	limit?: number;
	sortBy?: string;
	sortOrder?: 'asc' | 'desc';
}
