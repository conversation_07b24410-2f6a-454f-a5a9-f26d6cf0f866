'use client';

import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

// Theme handling utils
export type Theme = 'light' | 'dark' | 'system';

export function getSystemTheme(): Theme {
	if (typeof window !== 'undefined') {
		return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
	}
	return 'light'; // Default to light if SSR
}

// Function to set the theme in localStorage and apply it to the document
export function setTheme(theme: Theme) {
	if (typeof window === 'undefined') return;

	// Save theme preference
	localStorage.setItem('theme', theme);

	// Apply theme
	const isDark = theme === 'dark' || (theme === 'system' && getSystemTheme() === 'dark');

	document.documentElement.classList.toggle('dark', isDark);
}

// Function to get the current theme from localStorage
export function getTheme(): Theme {
	if (typeof window === 'undefined') return 'system';
	return (localStorage.getItem('theme') as Theme) || 'system';
}

// Format currency
export function formatCurrency(amount: number, currency = 'VND'): string {
	return new Intl.NumberFormat('vi-VN', {
		style: 'currency',
		currency,
	}).format(amount);
}

// Format number
export function formatNumber(num: number): string {
	return new Intl.NumberFormat('vi-VN').format(num);
}

// Truncate text
export function truncateText(text: string, maxLength: number): string {
	if (text.length <= maxLength) return text;
	return text.slice(0, maxLength) + '...';
}

// Generate slug from text
export function generateSlug(text: string): string {
	return text
		.toLowerCase()
		.normalize('NFD')
		.replace(/[\u0300-\u036f]/g, '')
		.replace(/[đĐ]/g, 'd')
		.replace(/[^a-z0-9\s-]/g, '')
		.replace(/\s+/g, '-')
		.replace(/-+/g, '-')
		.trim();
}
