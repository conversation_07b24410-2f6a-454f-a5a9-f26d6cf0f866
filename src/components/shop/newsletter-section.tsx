'use client';

import { useState } from 'react';
import { Mail, Gift } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function NewsletterSection() {
	const [email, setEmail] = useState('');
	const [isSubscribed, setIsSubscribed] = useState(false);

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		// Handle newsletter subscription
		setIsSubscribed(true);
		setEmail('');
		
		// Reset after 3 seconds
		setTimeout(() => {
			setIsSubscribed(false);
		}, 3000);
	};

	return (
		<section className="py-16 lg:py-24">
			<div className="container mx-auto px-4">
				<div className="max-w-4xl mx-auto">
					<div className="bg-gradient-to-br from-fashion-500 to-fashion-600 rounded-2xl p-8 lg:p-12 text-white relative overflow-hidden">
						{/* Background Pattern */}
						<div className="absolute inset-0 bg-white/5 bg-grid-pattern" />
						<div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full blur-3xl -translate-y-32 translate-x-32" />
						<div className="absolute bottom-0 left-0 w-48 h-48 bg-white/10 rounded-full blur-3xl translate-y-24 -translate-x-24" />

						<div className="relative z-10">
							<div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
								{/* Content */}
								<div className="space-y-6">
									<div className="flex items-center space-x-3">
										<div className="p-3 bg-white/20 rounded-full">
											<Gift className="h-6 w-6" />
										</div>
										<span className="text-sm font-medium bg-white/20 px-3 py-1 rounded-full">
											Ưu đãi đặc biệt
										</span>
									</div>

									<div className="space-y-4">
										<h2 className="text-3xl lg:text-4xl font-bold leading-tight">
											Đăng ký nhận tin và nhận ngay 
											<span className="block text-yellow-300">voucher 100.000đ</span>
										</h2>
										<p className="text-lg text-white/90">
											Cập nhật những xu hướng thời trang mới nhất, ưu đãi độc quyền 
											và những bộ sưu tập đặc biệt chỉ dành cho thành viên.
										</p>
									</div>

									{/* Benefits */}
									<div className="space-y-3">
										<div className="flex items-center space-x-3">
											<div className="w-2 h-2 bg-yellow-300 rounded-full" />
											<span className="text-sm">Ưu đãi độc quyền cho thành viên</span>
										</div>
										<div className="flex items-center space-x-3">
											<div className="w-2 h-2 bg-yellow-300 rounded-full" />
											<span className="text-sm">Thông tin sản phẩm mới sớm nhất</span>
										</div>
										<div className="flex items-center space-x-3">
											<div className="w-2 h-2 bg-yellow-300 rounded-full" />
											<span className="text-sm">Tips phối đồ từ chuyên gia</span>
										</div>
									</div>
								</div>

								{/* Newsletter Form */}
								<div className="space-y-6">
									{isSubscribed ? (
										<div className="text-center space-y-4">
											<div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto">
												<Mail className="h-8 w-8 text-white" />
											</div>
											<div>
												<h3 className="text-xl font-semibold mb-2">Đăng ký thành công!</h3>
												<p className="text-white/90">
													Cảm ơn bạn đã đăng ký. Voucher sẽ được gửi đến email của bạn trong vài phút.
												</p>
											</div>
										</div>
									) : (
										<form onSubmit={handleSubmit} className="space-y-4">
											<div className="space-y-3">
												<label htmlFor="newsletter-email" className="block text-sm font-medium">
													Địa chỉ email của bạn
												</label>
												<div className="relative">
													<Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
													<input
														id="newsletter-email"
														type="email"
														value={email}
														onChange={(e) => setEmail(e.target.value)}
														placeholder="<EMAIL>"
														className="w-full pl-12 pr-4 py-3 bg-white text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-300"
														required
													/>
												</div>
											</div>
											
											<Button 
												type="submit" 
												className="w-full bg-white text-fashion-600 hover:bg-white/90 font-semibold py-3"
												size="lg"
											>
												Đăng ký ngay và nhận voucher
											</Button>
										</form>
									)}

									<p className="text-xs text-white/70 text-center">
										Bằng cách đăng ký, bạn đồng ý với{' '}
										<a href="/privacy" className="underline hover:text-white">
											Chính sách bảo mật
										</a>{' '}
										của chúng tôi.
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
	);
}
