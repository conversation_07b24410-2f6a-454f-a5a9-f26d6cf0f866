import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';

// GET /api/admin/orders - <PERSON><PERSON><PERSON> danh sách đơn hàng cho admin
export async function GET(request: NextRequest) {
	try {
		const session = await getServerSession(authOptions);

		if (!session || session.user.role !== 'ADMIN') {
			return NextResponse.json(
				{ error: 'Không có quyền truy cập' },
				{ status: 403 }
			);
		}

		const { searchParams } = new URL(request.url);
		const page = parseInt(searchParams.get('page') || '1');
		const limit = parseInt(searchParams.get('limit') || '20');
		const search = searchParams.get('search');
		const status = searchParams.get('status');
		const paymentMethod = searchParams.get('paymentMethod');
		const paymentStatus = searchParams.get('paymentStatus');

		const skip = (page - 1) * limit;

		// Build where clause
		const where: any = {};

		if (search) {
			where.OR = [
				{ id: { contains: search, mode: 'insensitive' } },
				{ user: { name: { contains: search, mode: 'insensitive' } } },
				{ user: { email: { contains: search, mode: 'insensitive' } } },
			];
		}

		if (status) {
			where.status = status;
		}

		if (paymentMethod) {
			where.paymentMethod = paymentMethod;
		}

		if (paymentStatus) {
			where.paymentStatus = paymentStatus;
		}

		// Get orders with pagination
		const [orders, total] = await Promise.all([
			prisma.order.findMany({
				where,
				include: {
					user: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
					items: {
						include: {
							product: {
								select: {
									id: true,
									name: true,
									images: true,
								},
							},
						},
					},
				},
				orderBy: {
					createdAt: 'desc',
				},
				skip,
				take: limit,
			}),
			prisma.order.count({ where }),
		]);

		return NextResponse.json({
			orders,
			pagination: {
				page,
				limit,
				total,
				pages: Math.ceil(total / limit),
			},
		});
	} catch (error) {
		console.error('Get admin orders error:', error);
		return NextResponse.json(
			{ error: 'Có lỗi xảy ra khi lấy danh sách đơn hàng' },
			{ status: 500 }
		);
	}
}
