import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/route';
import { z } from 'zod';

const createReviewSchema = z.object({
	productId: z.string().min(1, 'Product ID là bắt buộc'),
	rating: z.number().min(1, 'Rating phải từ 1-5').max(5, 'Rating phải từ 1-5'),
	comment: z.string().optional(),
	images: z.array(z.string()).optional(),
});

// GET /api/reviews - <PERSON><PERSON><PERSON> s<PERSON>ch reviews
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const productId = searchParams.get('productId');
		const page = parseInt(searchParams.get('page') || '1');
		const limit = parseInt(searchParams.get('limit') || '10');

		const skip = (page - 1) * limit;

		// Build where clause
		const where: any = {};

		if (productId) {
			where.productId = productId;
		}

		// Get reviews with pagination
		const [reviews, total] = await Promise.all([
			prisma.review.findMany({
				where,
				include: {
					user: {
						select: {
							id: true,
							name: true,
							avatar: true,
						},
					},
					product: {
						select: {
							id: true,
							name: true,
							slug: true,
						},
					},
				},
				orderBy: {
					createdAt: 'desc',
				},
				skip,
				take: limit,
			}),
			prisma.review.count({ where }),
		]);

		return NextResponse.json({
			reviews,
			pagination: {
				page,
				limit,
				total,
				pages: Math.ceil(total / limit),
			},
		});
	} catch (error) {
		console.error('Get reviews error:', error);
		return NextResponse.json(
			{ error: 'Có lỗi xảy ra khi lấy danh sách đánh giá' },
			{ status: 500 }
		);
	}
}

// POST /api/reviews - Tạo review mới
export async function POST(request: NextRequest) {
	try {
		const session = await getServerSession(authOptions);

		if (!session) {
			return NextResponse.json(
				{ error: 'Vui lòng đăng nhập' },
				{ status: 401 }
			);
		}

		const body = await request.json();
		const data = createReviewSchema.parse(body);

		// Kiểm tra xem user đã mua sản phẩm này chưa
		const orderItem = await prisma.orderItem.findFirst({
			where: {
				productId: data.productId,
				order: {
					userId: session.user.id,
					status: 'DELIVERED', // Chỉ cho phép review khi đã nhận hàng
				},
			},
		});

		if (!orderItem) {
			return NextResponse.json(
				{ error: 'Bạn chỉ có thể đánh giá sản phẩm đã mua và nhận hàng' },
				{ status: 400 }
			);
		}

		// Kiểm tra xem user đã review sản phẩm này chưa
		const existingReview = await prisma.review.findFirst({
			where: {
				userId: session.user.id,
				productId: data.productId,
			},
		});

		if (existingReview) {
			return NextResponse.json(
				{ error: 'Bạn đã đánh giá sản phẩm này rồi' },
				{ status: 400 }
			);
		}

		// Tạo review
		const review = await prisma.review.create({
			data: {
				userId: session.user.id,
				productId: data.productId,
				rating: data.rating,
				comment: data.comment,
				images: data.images || [],
			},
			include: {
				user: {
					select: {
						id: true,
						name: true,
						avatar: true,
					},
				},
				product: {
					select: {
						id: true,
						name: true,
						slug: true,
					},
				},
			},
		});

		// Cập nhật avgRating và reviewCount của product
		const reviews = await prisma.review.findMany({
			where: { productId: data.productId },
			select: { rating: true },
		});

		const avgRating = reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length;

		await prisma.product.update({
			where: { id: data.productId },
			data: {
				avgRating: Math.round(avgRating * 10) / 10, // Round to 1 decimal place
				reviewCount: reviews.length,
			},
		});

		return NextResponse.json(
			{
				message: 'Đánh giá thành công',
				review,
			},
			{ status: 201 }
		);
	} catch (error) {
		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{ error: error.errors[0].message },
				{ status: 400 }
			);
		}

		console.error('Create review error:', error);
		return NextResponse.json(
			{ error: 'Có lỗi xảy ra khi tạo đánh giá' },
			{ status: 500 }
		);
	}
}
