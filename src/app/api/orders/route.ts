import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/route';
import { z } from 'zod';

const createOrderSchema = z.object({
	shippingAddress: z.object({
		fullName: z.string().min(1, 'H<PERSON> tên là bắt buộc'),
		phone: z.string().min(1, 'Số điện thoại là bắt buộc'),
		address: z.string().min(1, 'Địa chỉ là bắt buộc'),
		ward: z.string().min(1, 'Ph<PERSON>ờng/Xã là bắt buộc'),
		district: z.string().min(1, 'Quận/Huyện là bắt buộc'),
		province: z.string().min(1, 'Tỉnh/Thành phố là bắt buộc'),
	}),
	billingAddress: z.object({
		fullName: z.string().min(1, '<PERSON><PERSON> tên là bắt buộc'),
		phone: z.string().min(1, '<PERSON><PERSON> điện thoại là bắt buộc'),
		address: z.string().min(1, 'Địa chỉ là bắt buộc'),
		ward: z.string().min(1, 'Phường/Xã là bắt buộc'),
		district: z.string().min(1, 'Quận/Huyện là bắt buộc'),
		province: z.string().min(1, 'Tỉnh/Thành phố là bắt buộc'),
	}).optional(),
	paymentMethod: z.enum(['COD', 'BANK_TRANSFER', 'CREDIT_CARD']),
	notes: z.string().optional(),
});

// GET /api/orders - Lấy danh sách đơn hàng của user
export async function GET(request: NextRequest) {
	try {
		const session = await getServerSession(authOptions);

		if (!session) {
			return NextResponse.json(
				{ error: 'Vui lòng đăng nhập' },
				{ status: 401 }
			);
		}

		const { searchParams } = new URL(request.url);
		const page = parseInt(searchParams.get('page') || '1');
		const limit = parseInt(searchParams.get('limit') || '10');
		const status = searchParams.get('status');

		const skip = (page - 1) * limit;

		// Build where clause
		const where: any = {
			userId: session.user.id,
		};

		if (status) {
			where.status = status;
		}

		// Get orders with pagination
		const [orders, total] = await Promise.all([
			prisma.order.findMany({
				where,
				include: {
					items: {
						include: {
							product: {
								select: {
									id: true,
									name: true,
									images: true,
									slug: true,
								},
							},
						},
					},
				},
				orderBy: {
					createdAt: 'desc',
				},
				skip,
				take: limit,
			}),
			prisma.order.count({ where }),
		]);

		return NextResponse.json({
			orders,
			pagination: {
				page,
				limit,
				total,
				pages: Math.ceil(total / limit),
			},
		});
	} catch (error) {
		console.error('Get orders error:', error);
		return NextResponse.json(
			{ error: 'Có lỗi xảy ra khi lấy danh sách đơn hàng' },
			{ status: 500 }
		);
	}
}

// POST /api/orders - Tạo đơn hàng mới
export async function POST(request: NextRequest) {
	try {
		const session = await getServerSession(authOptions);

		if (!session) {
			return NextResponse.json(
				{ error: 'Vui lòng đăng nhập' },
				{ status: 401 }
			);
		}

		const body = await request.json();
		const data = createOrderSchema.parse(body);

		// Lấy giỏ hàng của user
		const cart = await prisma.cart.findUnique({
			where: { userId: session.user.id },
			include: {
				items: {
					include: {
						product: true,
					},
				},
			},
		});

		if (!cart || cart.items.length === 0) {
			return NextResponse.json(
				{ error: 'Giỏ hàng trống' },
				{ status: 400 }
			);
		}

		// Kiểm tra stock và tính tổng tiền
		let total = 0;
		const orderItems = [];

		for (const item of cart.items) {
			if (item.product.status !== 'ACTIVE') {
				return NextResponse.json(
					{ error: `Sản phẩm ${item.product.name} không còn khả dụng` },
					{ status: 400 }
				);
			}

			if (item.product.stock < item.quantity) {
				return NextResponse.json(
					{ error: `Sản phẩm ${item.product.name} không đủ hàng trong kho` },
					{ status: 400 }
				);
			}

			const price = item.product.salePrice || item.product.price;
			total += price * item.quantity;

			orderItems.push({
				productId: item.productId,
				quantity: item.quantity,
				price: price,
			});
		}

		// Tạo đơn hàng trong transaction
		const order = await prisma.$transaction(async (tx) => {
			// Tạo đơn hàng
			const newOrder = await tx.order.create({
				data: {
					userId: session.user.id,
					total,
					shippingAddress: data.shippingAddress,
					billingAddress: data.billingAddress,
					paymentMethod: data.paymentMethod,
					notes: data.notes,
					items: {
						create: orderItems,
					},
				},
				include: {
					items: {
						include: {
							product: {
								select: {
									id: true,
									name: true,
									images: true,
									slug: true,
								},
							},
						},
					},
				},
			});

			// Cập nhật stock
			for (const item of cart.items) {
				await tx.product.update({
					where: { id: item.productId },
					data: {
						stock: {
							decrement: item.quantity,
						},
					},
				});
			}

			// Xóa giỏ hàng
			await tx.cartItem.deleteMany({
				where: { cartId: cart.id },
			});

			return newOrder;
		});

		return NextResponse.json(
			{
				message: 'Đặt hàng thành công',
				order,
			},
			{ status: 201 }
		);
	} catch (error) {
		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{ error: error.errors[0].message },
				{ status: 400 }
			);
		}

		console.error('Create order error:', error);
		return NextResponse.json(
			{ error: 'Có lỗi xảy ra khi tạo đơn hàng' },
			{ status: 500 }
		);
	}
}
