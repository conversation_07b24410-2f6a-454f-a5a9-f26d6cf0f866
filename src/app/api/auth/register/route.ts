import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const registerSchema = z.object({
	name: z.string().min(2, 'Tên phải có ít nhất 2 ký tự'),
	email: z.string().email('<PERSON><PERSON> không hợp lệ'),
	password: z.string().min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),
});

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { name, email, password } = registerSchema.parse(body);

		// Kiểm tra email đã tồn tại
		const existingUser = await prisma.user.findUnique({
			where: { email },
		});

		if (existingUser) {
			return NextResponse.json(
				{ error: '<PERSON><PERSON> đ<PERSON> đ<PERSON> sử dụng' },
				{ status: 400 }
			);
		}

		// Hash password
		const hashedPassword = await bcrypt.hash(password, 12);

		// Tạo user mới
		const user = await prisma.user.create({
			data: {
				name,
				email,
				password: hashedPassword,
			},
			select: {
				id: true,
				name: true,
				email: true,
				role: true,
				createdAt: true,
			},
		});

		return NextResponse.json(
			{
				message: 'Đăng ký thành công',
				user,
			},
			{ status: 201 }
		);
	} catch (error) {
		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{ error: error.errors[0].message },
				{ status: 400 }
			);
		}

		console.error('Register error:', error);
		return NextResponse.json(
			{ error: 'Có lỗi xảy ra khi đăng ký' },
			{ status: 500 }
		);
	}
}
