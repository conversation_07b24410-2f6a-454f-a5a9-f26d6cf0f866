import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/settings - Lấy settings công khai (không cần auth)
export async function GET(request: NextRequest) {
  try {
    // Get all settings from database
    const settings = await prisma.setting.findMany();
    
    // Convert to key-value object
    const settingsObject: any = {};
    
    settings.forEach(setting => {
      // Only return public settings (exclude sensitive data)
      if (!isPrivateSetting(setting.key)) {
        settingsObject[setting.key] = setting.value;
      }
    });

    return NextResponse.json(settingsObject);
  } catch (error) {
    console.error('Get public settings error:', error);
    return NextResponse.json(
      { error: 'Có lỗi xảy ra khi lấy cài đặt' },
      { status: 500 }
    );
  }
}

// Helper function to determine if a setting is private
function isPrivateSetting(key: string): boolean {
  const privateSettings = [
    'emailSettings', // Contains SMTP credentials
    'securitySettings', // Contains security configs
  ];
  
  return privateSettings.includes(key);
}
