"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON>, Footer } from "@/components/layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { AdvancedSearch } from "@/components/search/advanced-search";
import {
  Search,
  Filter,
  Grid,
  List,
  Star,
  Heart,
  ShoppingCart,
  Package,
  SlidersHorizontal,
} from "lucide-react";
import { toast } from "@/lib/toast";

interface Product {
  id: string;
  name: string;
  price: number;
  salePrice?: number;
  images: string[];
  slug: string;
  avgRating: number;
  reviewCount: number;
  stock: number;
  category: {
    id: string;
    name: string;
  };
}

interface Category {
  id: string;
  name: string;
  children?: Category[];
}

export default function SearchPage() {
  const searchParams = useSearchParams();
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0,
  });

  // Get search query from URL
  const searchQuery = searchParams.get("search") || "";
  const categoryFilter = searchParams.get("category") || "";

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    fetchProducts();
  }, [searchParams, pagination.page]);

  const fetchCategories = async () => {
    try {
      const response = await fetch("/api/categories");
      const data = await response.json();

      if (response.ok) {
        setCategories(data);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  const fetchProducts = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams(searchParams.toString());
      params.set("page", pagination.page.toString());
      params.set("limit", pagination.limit.toString());

      const response = await fetch(`/api/products?${params}`);
      const data = await response.json();

      if (response.ok) {
        setProducts(data.products);
        setPagination(data.pagination);
      } else {
        toast.error("Có lỗi xảy ra khi tìm kiếm sản phẩm");
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi tìm kiếm sản phẩm");
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(price);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating)
            ? "text-yellow-400 fill-current"
            : "text-gray-300"
        }`}
      />
    ));
  };

  const getSearchSummary = () => {
    const filters = [];
    if (searchQuery) filters.push(`"${searchQuery}"`);
    if (categoryFilter) {
      const category = categories.find((c) => c.id === categoryFilter);
      if (category) filters.push(`trong ${category.name}`);
    }

    return filters.length > 0 ? filters.join(" ") : "tất cả sản phẩm";
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        {/* Search Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Kết quả tìm kiếm</h1>
          <p className="text-muted-foreground">
            Tìm thấy {pagination.total} sản phẩm cho {getSearchSummary()}
          </p>
        </div>

        {/* Advanced Search Toggle */}
        <div className="mb-6">
          <Button
            variant="outline"
            onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
            className="mb-4"
          >
            <SlidersHorizontal className="h-4 w-4 mr-2" />
            {showAdvancedSearch ? "Ẩn" : "Hiện"} tìm kiếm nâng cao
          </Button>

          {showAdvancedSearch && (
            <AdvancedSearch
              categories={categories}
              onClose={() => setShowAdvancedSearch(false)}
            />
          )}
        </div>

        {/* Results Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <span className="text-sm text-muted-foreground">
              Hiển thị {products.length} trong {pagination.total} sản phẩm
            </span>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === "grid" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("grid")}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Products Grid/List */}
        {loading ? (
          <div
            className={
              viewMode === "grid"
                ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
                : "space-y-4"
            }
          >
            {Array.from({ length: 8 }, (_, i) => (
              <Card key={i} className="animate-pulse">
                <div className={`${viewMode === "list" ? "flex" : ""}`}>
                  <div
                    className={`${
                      viewMode === "list" ? "w-48 h-48" : "aspect-square"
                    } bg-gray-200 rounded-t-lg`}
                  />
                  <CardContent
                    className={`p-4 space-y-2 ${
                      viewMode === "list" ? "flex-1" : ""
                    }`}
                  >
                    <div className="h-4 bg-gray-200 rounded" />
                    <div className="h-4 bg-gray-200 rounded w-2/3" />
                    <div className="h-4 bg-gray-200 rounded w-1/2" />
                  </CardContent>
                </div>
              </Card>
            ))}
          </div>
        ) : products.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Search className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                Không tìm thấy sản phẩm nào
              </h3>
              <p className="text-muted-foreground mb-6">
                Hãy thử tìm kiếm với từ khóa khác hoặc điều chỉnh bộ lọc
              </p>
              <div className="flex gap-2 justify-center">
                <Button
                  variant="outline"
                  onClick={() => setShowAdvancedSearch(true)}
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Điều chỉnh bộ lọc
                </Button>
                <Link href="/products">
                  <Button className="bg-pink-600 hover:bg-pink-700">
                    Xem tất cả sản phẩm
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div
            className={
              viewMode === "grid"
                ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
                : "space-y-4"
            }
          >
            {products.map((product) => (
              <Card
                key={product.id}
                className={`group hover:shadow-lg transition-shadow ${
                  viewMode === "list" ? "flex" : ""
                }`}
              >
                <Link href={`/products/${product.slug}`} className="block">
                  <div
                    className={`relative overflow-hidden ${
                      viewMode === "list"
                        ? "w-48 h-48 flex-shrink-0"
                        : "aspect-square"
                    } rounded-t-lg`}
                  >
                    <Image
                      src={product.images[0] || "/images/placeholder.jpg"}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    {product.salePrice && (
                      <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                        -
                        {Math.round(
                          ((product.price - product.salePrice) /
                            product.price) *
                            100
                        )}
                        %
                      </div>
                    )}
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button
                        size="sm"
                        variant="secondary"
                        className="h-8 w-8 p-0"
                      >
                        <Heart className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </Link>

                <CardContent
                  className={`p-4 ${viewMode === "list" ? "flex-1" : ""}`}
                >
                  <Link href={`/products/${product.slug}`}>
                    <h3 className="font-medium text-sm mb-2 line-clamp-2 hover:text-pink-600 transition-colors">
                      {product.name}
                    </h3>
                  </Link>

                  <div className="text-xs text-muted-foreground mb-2">
                    {product.category.name}
                  </div>

                  <div className="flex items-center gap-1 mb-2">
                    {renderStars(product.avgRating)}
                    <span className="text-xs text-muted-foreground ml-1">
                      ({product.reviewCount})
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      {product.salePrice ? (
                        <>
                          <div className="text-lg font-bold text-pink-600">
                            {formatPrice(product.salePrice)}
                          </div>
                          <div className="text-sm text-muted-foreground line-through">
                            {formatPrice(product.price)}
                          </div>
                        </>
                      ) : (
                        <div className="text-lg font-bold">
                          {formatPrice(product.price)}
                        </div>
                      )}
                    </div>

                    <Button
                      size="sm"
                      className="bg-pink-600 hover:bg-pink-700 h-8 w-8 p-0"
                      onClick={(e) => {
                        e.preventDefault();
                        toast.success("Đã thêm vào giỏ hàng");
                      }}
                    >
                      <ShoppingCart className="h-4 w-4" />
                    </Button>
                  </div>

                  {product.stock === 0 && (
                    <div className="mt-2 text-xs text-red-600 font-medium">
                      Hết hàng
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="flex justify-center items-center gap-2 mt-8">
            <Button
              variant="outline"
              disabled={pagination.page === 1}
              onClick={() =>
                setPagination({ ...pagination, page: pagination.page - 1 })
              }
            >
              Trước
            </Button>

            {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
              const page = i + 1;
              return (
                <Button
                  key={page}
                  variant={pagination.page === page ? "default" : "outline"}
                  onClick={() => setPagination({ ...pagination, page })}
                >
                  {page}
                </Button>
              );
            })}

            <Button
              variant="outline"
              disabled={pagination.page === pagination.pages}
              onClick={() =>
                setPagination({ ...pagination, page: pagination.page + 1 })
              }
            >
              Sau
            </Button>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
}
