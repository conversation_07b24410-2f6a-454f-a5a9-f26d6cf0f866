'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Header, Footer } from '@/components/layout';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowRight, Package } from 'lucide-react';
import { toast } from 'sonner';

interface Category {
	id: string;
	name: string;
	description?: string;
	image?: string;
	slug: string;
	_count: {
		products: number;
		children: number;
	};
	children: Category[];
	products?: Array<{
		id: string;
		name: string;
		price: number;
		salePrice?: number;
		images: string[];
		slug: string;
	}>;
}

export default function CategoriesPage() {
	const [categories, setCategories] = useState<Category[]>([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		const fetchCategories = async () => {
			try {
				const response = await fetch('/api/categories?includeProducts=true');
				const data = await response.json();

				if (response.ok) {
					// Filter only root categories (no parent)
					const rootCategories = data.filter((cat: Category) => !cat.parent);
					setCategories(rootCategories);
				} else {
					toast.error('Có lỗi xảy ra khi tải danh mục');
				}
			} catch (error) {
				toast.error('Có lỗi xảy ra khi tải danh mục');
			} finally {
				setLoading(false);
			}
		};

		fetchCategories();
	}, []);

	if (loading) {
		return (
			<div className="min-h-screen flex flex-col">
				<Header />
				<main className="flex-1 container mx-auto px-4 py-8">
					<div className="animate-pulse">
						<div className="h-8 bg-gray-200 rounded w-1/3 mb-8" />
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
							{Array.from({ length: 6 }, (_, i) => (
								<Card key={i}>
									<div className="aspect-video bg-gray-200 rounded-t-lg" />
									<CardContent className="p-4 space-y-2">
										<div className="h-4 bg-gray-200 rounded" />
										<div className="h-4 bg-gray-200 rounded w-2/3" />
									</CardContent>
								</Card>
							))}
						</div>
					</div>
				</main>
				<Footer />
			</div>
		);
	}

	return (
		<div className="min-h-screen flex flex-col">
			<Header />

			<main className="flex-1 container mx-auto px-4 py-8">
				{/* Page Header */}
				<div className="mb-8">
					<h1 className="text-3xl font-bold mb-2">Danh mục sản phẩm</h1>
					<p className="text-muted-foreground">
						Khám phá các danh mục thời trang đa dạng của chúng tôi
					</p>
				</div>

				{/* Categories Grid */}
				{categories.length === 0 ? (
					<div className="text-center py-12">
						<Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
						<h2 className="text-xl font-semibold mb-2">Chưa có danh mục nào</h2>
						<p className="text-muted-foreground">
							Các danh mục sản phẩm sẽ được hiển thị ở đây
						</p>
					</div>
				) : (
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						{categories.map((category) => (
							<Card key={category.id} className="group hover:shadow-lg transition-shadow">
								<Link href={`/categories/${category.slug}`}>
									<div className="relative aspect-video overflow-hidden rounded-t-lg bg-gradient-to-br from-pink-100 to-purple-100">
										{category.image ? (
											<Image
												src={category.image}
												alt={category.name}
												fill
												className="object-cover group-hover:scale-105 transition-transform duration-300"
											/>
										) : (
											<div className="flex items-center justify-center h-full">
												<Package className="h-16 w-16 text-pink-400" />
											</div>
										)}
										<div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors" />
										<div className="absolute bottom-4 left-4 text-white">
											<h3 className="text-xl font-bold mb-1">{category.name}</h3>
											<p className="text-sm opacity-90">
												{category._count.products} sản phẩm
											</p>
										</div>
									</div>
								</Link>

								<CardContent className="p-4">
									<div className="flex items-center justify-between mb-3">
										<Link href={`/categories/${category.slug}`}>
											<h3 className="font-semibold hover:text-pink-600 transition-colors">
												{category.name}
											</h3>
										</Link>
										<Link
											href={`/categories/${category.slug}`}
											className="text-pink-600 hover:text-pink-700 transition-colors"
										>
											<ArrowRight className="h-4 w-4" />
										</Link>
									</div>

									{category.description && (
										<p className="text-sm text-muted-foreground mb-3 line-clamp-2">
											{category.description}
										</p>
									)}

									{/* Subcategories */}
									{category.children.length > 0 && (
										<div className="mb-3">
											<p className="text-xs font-medium text-muted-foreground mb-2">
												Danh mục con:
											</p>
											<div className="flex flex-wrap gap-1">
												{category.children.slice(0, 3).map((child) => (
													<Link
														key={child.id}
														href={`/categories/${child.slug}`}
														className="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded transition-colors"
													>
														{child.name}
													</Link>
												))}
												{category.children.length > 3 && (
													<span className="text-xs text-muted-foreground px-2 py-1">
														+{category.children.length - 3} khác
													</span>
												)}
											</div>
										</div>
									)}

									{/* Featured Products */}
									{category.products && category.products.length > 0 && (
										<div>
											<p className="text-xs font-medium text-muted-foreground mb-2">
												Sản phẩm nổi bật:
											</p>
											<div className="grid grid-cols-4 gap-1">
												{category.products.slice(0, 4).map((product) => (
													<Link
														key={product.id}
														href={`/products/${product.slug}`}
														className="relative aspect-square overflow-hidden rounded bg-gray-100 hover:opacity-80 transition-opacity"
													>
														<Image
															src={product.images[0] || '/images/placeholder.jpg'}
															alt={product.name}
															fill
															className="object-cover"
														/>
													</Link>
												))}
											</div>
										</div>
									)}

									<div className="flex items-center justify-between mt-3 pt-3 border-t">
										<span className="text-sm text-muted-foreground">
											{category._count.products} sản phẩm
										</span>
										{category._count.children > 0 && (
											<span className="text-sm text-muted-foreground">
												{category._count.children} danh mục con
											</span>
										)}
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				)}

				{/* Popular Categories Section */}
				<div className="mt-16">
					<h2 className="text-2xl font-bold mb-6">Danh mục phổ biến</h2>
					<div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
						{categories
							.sort((a, b) => b._count.products - a._count.products)
							.slice(0, 6)
							.map((category) => (
								<Link
									key={category.id}
									href={`/categories/${category.slug}`}
									className="group"
								>
									<Card className="hover:shadow-md transition-shadow">
										<CardContent className="p-4 text-center">
											<div className="relative w-16 h-16 mx-auto mb-3 overflow-hidden rounded-full bg-gradient-to-br from-pink-100 to-purple-100">
												{category.image ? (
													<Image
														src={category.image}
														alt={category.name}
														fill
														className="object-cover group-hover:scale-110 transition-transform duration-300"
													/>
												) : (
													<div className="flex items-center justify-center h-full">
														<Package className="h-8 w-8 text-pink-400" />
													</div>
												)}
											</div>
											<h3 className="font-medium text-sm mb-1 group-hover:text-pink-600 transition-colors">
												{category.name}
											</h3>
											<p className="text-xs text-muted-foreground">
												{category._count.products} sản phẩm
											</p>
										</CardContent>
									</Card>
								</Link>
							))}
					</div>
				</div>
			</main>

			<Footer />
		</div>
	);
}
