'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
	Search, 
	Filter, 
	Eye, 
	Mail,
	Phone,
	Calendar,
	ShoppingCart,
	User,
	Crown
} from 'lucide-react';
import { toast } from 'sonner';

interface Customer {
	id: string;
	name: string;
	email: string;
	phone?: string;
	role: string;
	avatar?: string;
	createdAt: string;
	_count: {
		orders: number;
	};
	orders: Array<{
		total: number;
	}>;
}

export default function AdminUsersPage() {
	const [customers, setCustomers] = useState<Customer[]>([]);
	const [loading, setLoading] = useState(true);
	const [filters, setFilters] = useState({
		search: '',
		role: '',
	});
	const [pagination, setPagination] = useState({
		page: 1,
		limit: 20,
		total: 0,
		pages: 0,
	});

	useEffect(() => {
		fetchCustomers();
	}, [filters, pagination.page]);

	const fetchCustomers = async () => {
		setLoading(true);
		try {
			const params = new URLSearchParams({
				page: pagination.page.toString(),
				limit: pagination.limit.toString(),
				...(filters.search && { search: filters.search }),
				...(filters.role && { role: filters.role }),
			});

			const response = await fetch(`/api/admin/users?${params}`);
			const data = await response.json();

			if (response.ok) {
				setCustomers(data.users);
				setPagination(data.pagination);
			} else {
				toast.error('Có lỗi xảy ra khi tải danh sách khách hàng');
			}
		} catch (error) {
			toast.error('Có lỗi xảy ra khi tải danh sách khách hàng');
		} finally {
			setLoading(false);
		}
	};

	const handleSearch = (e: React.FormEvent) => {
		e.preventDefault();
		setPagination({ ...pagination, page: 1 });
		fetchCustomers();
	};

	const handleFilterChange = (key: string, value: string) => {
		setFilters({ ...filters, [key]: value });
		setPagination({ ...pagination, page: 1 });
	};

	const formatPrice = (price: number) => {
		return new Intl.NumberFormat('vi-VN', {
			style: 'currency',
			currency: 'VND',
		}).format(price);
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('vi-VN', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	};

	const getTotalSpent = (orders: Array<{ total: number }>) => {
		return orders.reduce((sum, order) => sum + order.total, 0);
	};

	const getRoleColor = (role: string) => {
		switch (role) {
			case 'ADMIN':
				return 'text-purple-600 bg-purple-100';
			case 'USER':
				return 'text-blue-600 bg-blue-100';
			default:
				return 'text-gray-600 bg-gray-100';
		}
	};

	const getRoleText = (role: string) => {
		switch (role) {
			case 'ADMIN':
				return 'Quản trị viên';
			case 'USER':
				return 'Khách hàng';
			default:
				return role;
		}
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div>
				<h1 className="text-3xl font-bold">Quản lý khách hàng</h1>
				<p className="text-muted-foreground">
					Quản lý thông tin và hoạt động của khách hàng
				</p>
			</div>

			{/* Filters */}
			<Card>
				<CardHeader>
					<CardTitle>Bộ lọc</CardTitle>
				</CardHeader>
				<CardContent>
					<form onSubmit={handleSearch} className="space-y-4">
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							{/* Search */}
							<div className="relative">
								<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
								<input
									type="text"
									placeholder="Tìm kiếm khách hàng..."
									value={filters.search}
									onChange={(e) => setFilters({ ...filters, search: e.target.value })}
									className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
								/>
							</div>

							{/* Role Filter */}
							<select
								value={filters.role}
								onChange={(e) => handleFilterChange('role', e.target.value)}
								className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
							>
								<option value="">Tất cả vai trò</option>
								<option value="USER">Khách hàng</option>
								<option value="ADMIN">Quản trị viên</option>
							</select>

							<Button type="submit" className="bg-pink-600 hover:bg-pink-700">
								<Filter className="h-4 w-4 mr-2" />
								Lọc
							</Button>
						</div>
					</form>
				</CardContent>
			</Card>

			{/* Customers Table */}
			<Card>
				<CardHeader>
					<CardTitle>
						Danh sách khách hàng ({pagination.total})
					</CardTitle>
				</CardHeader>
				<CardContent>
					{loading ? (
						<div className="space-y-4">
							{Array.from({ length: 5 }, (_, i) => (
								<div key={i} className="animate-pulse">
									<div className="flex items-center space-x-4">
										<div className="w-12 h-12 bg-gray-200 rounded-full" />
										<div className="flex-1 space-y-2">
											<div className="h-4 bg-gray-200 rounded" />
											<div className="h-4 bg-gray-200 rounded w-2/3" />
										</div>
									</div>
								</div>
							))}
						</div>
					) : customers.length === 0 ? (
						<div className="text-center py-12">
							<User className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
							<h3 className="text-lg font-semibold mb-2">Không có khách hàng nào</h3>
							<p className="text-muted-foreground">
								Chưa có khách hàng nào đăng ký
							</p>
						</div>
					) : (
						<div className="overflow-x-auto">
							<table className="w-full">
								<thead>
									<tr className="border-b">
										<th className="text-left py-3 px-4">Khách hàng</th>
										<th className="text-left py-3 px-4">Liên hệ</th>
										<th className="text-left py-3 px-4">Vai trò</th>
										<th className="text-left py-3 px-4">Đơn hàng</th>
										<th className="text-left py-3 px-4">Tổng chi tiêu</th>
										<th className="text-left py-3 px-4">Ngày đăng ký</th>
										<th className="text-left py-3 px-4">Thao tác</th>
									</tr>
								</thead>
								<tbody>
									{customers.map((customer) => (
										<tr key={customer.id} className="border-b hover:bg-gray-50">
											<td className="py-3 px-4">
												<div className="flex items-center space-x-3">
													<div className="relative w-10 h-10 flex-shrink-0">
														{customer.avatar ? (
															<Image
																src={customer.avatar}
																alt={customer.name}
																fill
																className="rounded-full object-cover"
															/>
														) : (
															<div className="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center">
																<span className="text-pink-600 font-medium">
																	{customer.name.charAt(0).toUpperCase()}
																</span>
															</div>
														)}
													</div>
													<div>
														<p className="font-medium">{customer.name}</p>
														<p className="text-sm text-muted-foreground">
															ID: {customer.id.slice(-8)}
														</p>
													</div>
												</div>
											</td>
											<td className="py-3 px-4">
												<div className="space-y-1">
													<div className="flex items-center gap-1 text-sm">
														<Mail className="h-3 w-3 text-muted-foreground" />
														<span>{customer.email}</span>
													</div>
													{customer.phone && (
														<div className="flex items-center gap-1 text-sm text-muted-foreground">
															<Phone className="h-3 w-3" />
															<span>{customer.phone}</span>
														</div>
													)}
												</div>
											</td>
											<td className="py-3 px-4">
												<span
													className={`px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1 w-fit ${getRoleColor(
														customer.role
													)}`}
												>
													{customer.role === 'ADMIN' ? (
														<Crown className="h-3 w-3" />
													) : (
														<User className="h-3 w-3" />
													)}
													{getRoleText(customer.role)}
												</span>
											</td>
											<td className="py-3 px-4">
												<div className="flex items-center gap-1">
													<ShoppingCart className="h-4 w-4 text-muted-foreground" />
													<span className="font-medium">{customer._count.orders}</span>
												</div>
											</td>
											<td className="py-3 px-4">
												<span className="font-semibold text-green-600">
													{formatPrice(getTotalSpent(customer.orders))}
												</span>
											</td>
											<td className="py-3 px-4 text-sm text-muted-foreground">
												<div className="flex items-center gap-1">
													<Calendar className="h-3 w-3" />
													{formatDate(customer.createdAt)}
												</div>
											</td>
											<td className="py-3 px-4">
												<Link href={`/admin/users/${customer.id}`}>
													<Button variant="ghost" size="sm">
														<Eye className="h-4 w-4" />
													</Button>
												</Link>
											</td>
										</tr>
									))}
								</tbody>
							</table>
						</div>
					)}

					{/* Pagination */}
					{pagination.pages > 1 && (
						<div className="flex justify-center items-center gap-2 mt-6">
							<Button
								variant="outline"
								disabled={pagination.page === 1}
								onClick={() => setPagination({ ...pagination, page: pagination.page - 1 })}
							>
								Trước
							</Button>

							{Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
								const page = i + 1;
								return (
									<Button
										key={page}
										variant={pagination.page === page ? 'default' : 'outline'}
										onClick={() => setPagination({ ...pagination, page })}
									>
										{page}
									</Button>
								);
							})}

							<Button
								variant="outline"
								disabled={pagination.page === pagination.pages}
								onClick={() => setPagination({ ...pagination, page: pagination.page + 1 })}
							>
								Sau
							</Button>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
