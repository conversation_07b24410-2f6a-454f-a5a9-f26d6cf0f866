'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
	Save, 
	ArrowLeft, 
	Upload, 
	X,
	Plus
} from 'lucide-react';
import { toast } from 'sonner';

interface Category {
	id: string;
	name: string;
	slug: string;
}

interface ProductForm {
	name: string;
	description: string;
	price: number;
	salePrice: number | null;
	sku: string;
	stock: number;
	categoryId: string;
	images: string[];
	featured: boolean;
	status: 'ACTIVE' | 'INACTIVE';
	tags: string[];
}

export default function CreateProductPage() {
	const router = useRouter();
	const [categories, setCategories] = useState<Category[]>([]);
	const [loading, setLoading] = useState(false);
	const [form, setForm] = useState<ProductForm>({
		name: '',
		description: '',
		price: 0,
		salePrice: null,
		sku: '',
		stock: 0,
		categoryId: '',
		images: [],
		featured: false,
		status: 'ACTIVE',
		tags: [],
	});
	const [newTag, setNewTag] = useState('');

	useEffect(() => {
		fetchCategories();
	}, []);

	const fetchCategories = async () => {
		try {
			const response = await fetch('/api/categories');
			const data = await response.json();
			if (response.ok) {
				setCategories(data);
			}
		} catch (error) {
			toast.error('Có lỗi xảy ra khi tải danh mục');
		}
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		
		if (!form.name || !form.description || !form.categoryId) {
			toast.error('Vui lòng điền đầy đủ thông tin bắt buộc');
			return;
		}

		setLoading(true);
		try {
			const response = await fetch('/api/admin/products', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					...form,
					slug: form.name
						.toLowerCase()
						.normalize('NFD')
						.replace(/[\u0300-\u036f]/g, '')
						.replace(/[^a-z0-9\s-]/g, '')
						.replace(/\s+/g, '-')
						.replace(/-+/g, '-')
						.trim(),
				}),
			});

			if (response.ok) {
				toast.success('Tạo sản phẩm thành công');
				router.push('/admin/products');
			} else {
				const data = await response.json();
				toast.error(data.error || 'Có lỗi xảy ra khi tạo sản phẩm');
			}
		} catch (error) {
			toast.error('Có lỗi xảy ra khi tạo sản phẩm');
		} finally {
			setLoading(false);
		}
	};

	const handleInputChange = (field: keyof ProductForm, value: any) => {
		setForm({ ...form, [field]: value });
	};

	const addTag = () => {
		if (newTag.trim() && !form.tags.includes(newTag.trim())) {
			setForm({ ...form, tags: [...form.tags, newTag.trim()] });
			setNewTag('');
		}
	};

	const removeTag = (tagToRemove: string) => {
		setForm({ ...form, tags: form.tags.filter(tag => tag !== tagToRemove) });
	};

	const addImage = () => {
		const url = prompt('Nhập URL hình ảnh:');
		if (url && url.trim()) {
			setForm({ ...form, images: [...form.images, url.trim()] });
		}
	};

	const removeImage = (index: number) => {
		setForm({ ...form, images: form.images.filter((_, i) => i !== index) });
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center gap-4">
				<Button
					variant="ghost"
					onClick={() => router.back()}
				>
					<ArrowLeft className="h-4 w-4 mr-2" />
					Quay lại
				</Button>
				<div>
					<h1 className="text-3xl font-bold">Thêm sản phẩm mới</h1>
					<p className="text-muted-foreground">
						Tạo sản phẩm mới cho cửa hàng
					</p>
				</div>
			</div>

			<form onSubmit={handleSubmit} className="space-y-6">
				<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
					{/* Main Info */}
					<div className="lg:col-span-2 space-y-6">
						<Card>
							<CardHeader>
								<CardTitle>Thông tin cơ bản</CardTitle>
							</CardHeader>
							<CardContent className="space-y-4">
								<div>
									<label className="block text-sm font-medium mb-2">
										Tên sản phẩm *
									</label>
									<input
										type="text"
										value={form.name}
										onChange={(e) => handleInputChange('name', e.target.value)}
										className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
										placeholder="Nhập tên sản phẩm"
										required
									/>
								</div>

								<div>
									<label className="block text-sm font-medium mb-2">
										Mô tả sản phẩm *
									</label>
									<textarea
										value={form.description}
										onChange={(e) => handleInputChange('description', e.target.value)}
										rows={4}
										className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
										placeholder="Nhập mô tả chi tiết sản phẩm"
										required
									/>
								</div>

								<div>
									<label className="block text-sm font-medium mb-2">
										SKU
									</label>
									<input
										type="text"
										value={form.sku}
										onChange={(e) => handleInputChange('sku', e.target.value)}
										className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
										placeholder="Mã sản phẩm (tự động tạo nếu để trống)"
									/>
								</div>
							</CardContent>
						</Card>

						{/* Pricing */}
						<Card>
							<CardHeader>
								<CardTitle>Giá bán</CardTitle>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<div>
										<label className="block text-sm font-medium mb-2">
											Giá gốc (VND) *
										</label>
										<input
											type="number"
											value={form.price}
											onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
											className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
											placeholder="0"
											min="0"
											required
										/>
									</div>

									<div>
										<label className="block text-sm font-medium mb-2">
											Giá khuyến mãi (VND)
										</label>
										<input
											type="number"
											value={form.salePrice || ''}
											onChange={(e) => handleInputChange('salePrice', e.target.value ? parseFloat(e.target.value) : null)}
											className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
											placeholder="Để trống nếu không có khuyến mãi"
											min="0"
										/>
									</div>
								</div>
							</CardContent>
						</Card>

						{/* Images */}
						<Card>
							<CardHeader>
								<CardTitle>Hình ảnh sản phẩm</CardTitle>
							</CardHeader>
							<CardContent className="space-y-4">
								<Button
									type="button"
									variant="outline"
									onClick={addImage}
									className="w-full"
								>
									<Plus className="h-4 w-4 mr-2" />
									Thêm hình ảnh
								</Button>

								{form.images.length > 0 && (
									<div className="grid grid-cols-2 md:grid-cols-3 gap-4">
										{form.images.map((image, index) => (
											<div key={index} className="relative group">
												<img
													src={image}
													alt={`Product ${index + 1}`}
													className="w-full h-32 object-cover rounded-lg border"
												/>
												<button
													type="button"
													onClick={() => removeImage(index)}
													className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
												>
													<X className="h-3 w-3" />
												</button>
											</div>
										))}
									</div>
								)}
							</CardContent>
						</Card>
					</div>

					{/* Sidebar */}
					<div className="space-y-6">
						{/* Status & Category */}
						<Card>
							<CardHeader>
								<CardTitle>Cài đặt</CardTitle>
							</CardHeader>
							<CardContent className="space-y-4">
								<div>
									<label className="block text-sm font-medium mb-2">
										Danh mục *
									</label>
									<select
										value={form.categoryId}
										onChange={(e) => handleInputChange('categoryId', e.target.value)}
										className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
										required
									>
										<option value="">Chọn danh mục</option>
										{categories.map((category) => (
											<option key={category.id} value={category.id}>
												{category.name}
											</option>
										))}
									</select>
								</div>

								<div>
									<label className="block text-sm font-medium mb-2">
										Số lượng tồn kho
									</label>
									<input
										type="number"
										value={form.stock}
										onChange={(e) => handleInputChange('stock', parseInt(e.target.value) || 0)}
										className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
										placeholder="0"
										min="0"
									/>
								</div>

								<div>
									<label className="block text-sm font-medium mb-2">
										Trạng thái
									</label>
									<select
										value={form.status}
										onChange={(e) => handleInputChange('status', e.target.value)}
										className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
									>
										<option value="ACTIVE">Hoạt động</option>
										<option value="INACTIVE">Tạm ẩn</option>
									</select>
								</div>

								<div className="flex items-center space-x-2">
									<input
										type="checkbox"
										id="featured"
										checked={form.featured}
										onChange={(e) => handleInputChange('featured', e.target.checked)}
										className="rounded border-gray-300 text-pink-600 focus:ring-pink-500"
									/>
									<label htmlFor="featured" className="text-sm font-medium">
										Sản phẩm nổi bật
									</label>
								</div>
							</CardContent>
						</Card>

						{/* Tags */}
						<Card>
							<CardHeader>
								<CardTitle>Tags</CardTitle>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="flex gap-2">
									<input
										type="text"
										value={newTag}
										onChange={(e) => setNewTag(e.target.value)}
										onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
										className="flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
										placeholder="Thêm tag"
									/>
									<Button type="button" onClick={addTag} size="sm">
										<Plus className="h-4 w-4" />
									</Button>
								</div>

								{form.tags.length > 0 && (
									<div className="flex flex-wrap gap-2">
										{form.tags.map((tag, index) => (
											<span
												key={index}
												className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 text-gray-700 text-sm rounded"
											>
												{tag}
												<button
													type="button"
													onClick={() => removeTag(tag)}
													className="text-gray-500 hover:text-red-500"
												>
													<X className="h-3 w-3" />
												</button>
											</span>
										))}
									</div>
								)}
							</CardContent>
						</Card>

						{/* Actions */}
						<Card>
							<CardContent className="pt-6">
								<div className="space-y-2">
									<Button
										type="submit"
										disabled={loading}
										className="w-full bg-pink-600 hover:bg-pink-700"
									>
										<Save className="h-4 w-4 mr-2" />
										{loading ? 'Đang tạo...' : 'Tạo sản phẩm'}
									</Button>
									<Button
										type="button"
										variant="outline"
										onClick={() => router.back()}
										className="w-full"
									>
										Hủy
									</Button>
								</div>
							</CardContent>
						</Card>
					</div>
				</div>
			</form>
		</div>
	);
}
