'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
	Plus, 
	Search, 
	Edit, 
	Trash2, 
	FileText,
	Eye,
	Calendar,
	User,
	Tag as TagIcon
} from 'lucide-react';
import { toast } from 'sonner';

interface Post {
	id: string;
	title: string;
	slug: string;
	excerpt: string;
	content: string;
	status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
	featured: boolean;
	tags: string[];
	createdAt: string;
	updatedAt: string;
	author: {
		name: string;
	};
	_count: {
		comments: number;
	};
}

export default function AdminPostsPage() {
	const [posts, setPosts] = useState<Post[]>([]);
	const [loading, setLoading] = useState(true);
	const [filters, setFilters] = useState({
		search: '',
		status: '',
	});

	useEffect(() => {
		fetchPosts();
	}, [filters]);

	const fetchPosts = async () => {
		setLoading(true);
		try {
			// Mock data for now since we don't have posts in the database yet
			const mockPosts: Post[] = [
				{
					id: '1',
					title: 'Xu hướng thời trang mùa hè 2024',
					slug: 'xu-huong-thoi-trang-mua-he-2024',
					excerpt: 'Khám phá những xu hướng thời trang hot nhất mùa hè này với những gam màu tươi sáng và chất liệu thoáng mát.',
					content: 'Nội dung bài viết...',
					status: 'PUBLISHED',
					featured: true,
					tags: ['thời trang', 'mùa hè', 'xu hướng'],
					createdAt: new Date().toISOString(),
					updatedAt: new Date().toISOString(),
					author: { name: 'Admin' },
					_count: { comments: 5 }
				},
				{
					id: '2',
					title: 'Cách phối đồ công sở thanh lịch',
					slug: 'cach-phoi-do-cong-so-thanh-lich',
					excerpt: 'Hướng dẫn chi tiết cách phối đồ công sở vừa thanh lịch vừa thời trang cho phái đẹp.',
					content: 'Nội dung bài viết...',
					status: 'PUBLISHED',
					featured: false,
					tags: ['công sở', 'phối đồ'],
					createdAt: new Date(Date.now() - 86400000).toISOString(),
					updatedAt: new Date(Date.now() - 86400000).toISOString(),
					author: { name: 'Admin' },
					_count: { comments: 3 }
				},
				{
					id: '3',
					title: 'Bí quyết chọn giày phù hợp với từng dáng người',
					slug: 'bi-quyet-chon-giay-phu-hop',
					excerpt: 'Những lời khuyên hữu ích giúp bạn chọn được đôi giày hoàn hảo cho dáng người của mình.',
					content: 'Nội dung bài viết...',
					status: 'DRAFT',
					featured: false,
					tags: ['giày', 'styling'],
					createdAt: new Date(Date.now() - 172800000).toISOString(),
					updatedAt: new Date(Date.now() - 172800000).toISOString(),
					author: { name: 'Admin' },
					_count: { comments: 0 }
				}
			];

			// Filter posts based on search and status
			let filteredPosts = mockPosts;
			
			if (filters.search) {
				filteredPosts = filteredPosts.filter(post =>
					post.title.toLowerCase().includes(filters.search.toLowerCase()) ||
					post.excerpt.toLowerCase().includes(filters.search.toLowerCase())
				);
			}

			if (filters.status) {
				filteredPosts = filteredPosts.filter(post => post.status === filters.status);
			}

			setPosts(filteredPosts);
		} catch (error) {
			toast.error('Có lỗi xảy ra khi tải danh sách bài viết');
		} finally {
			setLoading(false);
		}
	};

	const handleDeletePost = async (postId: string, postTitle: string) => {
		if (!confirm(`Bạn có chắc chắn muốn xóa bài viết "${postTitle}"?`)) {
			return;
		}

		try {
			// Mock delete
			setPosts(posts.filter(post => post.id !== postId));
			toast.success('Xóa bài viết thành công');
		} catch (error) {
			toast.error('Có lỗi xảy ra khi xóa bài viết');
		}
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('vi-VN', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'PUBLISHED':
				return 'text-green-600 bg-green-100';
			case 'DRAFT':
				return 'text-yellow-600 bg-yellow-100';
			case 'ARCHIVED':
				return 'text-gray-600 bg-gray-100';
			default:
				return 'text-gray-600 bg-gray-100';
		}
	};

	const getStatusText = (status: string) => {
		switch (status) {
			case 'PUBLISHED':
				return 'Đã xuất bản';
			case 'DRAFT':
				return 'Bản nháp';
			case 'ARCHIVED':
				return 'Đã lưu trữ';
			default:
				return status;
		}
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Quản lý bài viết</h1>
					<p className="text-muted-foreground">
						Quản lý blog và nội dung marketing
					</p>
				</div>
				<Link href="/admin/posts/create">
					<Button className="bg-pink-600 hover:bg-pink-700">
						<Plus className="h-4 w-4 mr-2" />
						Viết bài mới
					</Button>
				</Link>
			</div>

			{/* Filters */}
			<Card>
				<CardHeader>
					<CardTitle>Bộ lọc</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						{/* Search */}
						<div className="relative">
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
							<input
								type="text"
								placeholder="Tìm kiếm bài viết..."
								value={filters.search}
								onChange={(e) => setFilters({ ...filters, search: e.target.value })}
								className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
							/>
						</div>

						{/* Status Filter */}
						<select
							value={filters.status}
							onChange={(e) => setFilters({ ...filters, status: e.target.value })}
							className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
						>
							<option value="">Tất cả trạng thái</option>
							<option value="PUBLISHED">Đã xuất bản</option>
							<option value="DRAFT">Bản nháp</option>
							<option value="ARCHIVED">Đã lưu trữ</option>
						</select>

						<Button 
							onClick={fetchPosts}
							variant="outline"
						>
							Làm mới
						</Button>
					</div>
				</CardContent>
			</Card>

			{/* Posts List */}
			<Card>
				<CardHeader>
					<CardTitle>
						Danh sách bài viết ({posts.length})
					</CardTitle>
				</CardHeader>
				<CardContent>
					{loading ? (
						<div className="space-y-4">
							{Array.from({ length: 3 }, (_, i) => (
								<div key={i} className="animate-pulse">
									<div className="flex items-start space-x-4 p-4 border rounded-lg">
										<div className="w-16 h-16 bg-gray-200 rounded" />
										<div className="flex-1 space-y-2">
											<div className="h-4 bg-gray-200 rounded w-3/4" />
											<div className="h-3 bg-gray-200 rounded w-1/2" />
											<div className="h-3 bg-gray-200 rounded w-1/4" />
										</div>
									</div>
								</div>
							))}
						</div>
					) : posts.length === 0 ? (
						<div className="text-center py-12">
							<FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
							<h3 className="text-lg font-semibold mb-2">Chưa có bài viết nào</h3>
							<p className="text-muted-foreground mb-4">
								Bắt đầu bằng cách viết bài viết đầu tiên
							</p>
							<Link href="/admin/posts/create">
								<Button className="bg-pink-600 hover:bg-pink-700">
									<Plus className="h-4 w-4 mr-2" />
									Viết bài đầu tiên
								</Button>
							</Link>
						</div>
					) : (
						<div className="space-y-4">
							{posts.map((post) => (
								<div key={post.id} className="flex items-start justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
									<div className="flex items-start gap-4 flex-1">
										<div className="w-16 h-16 bg-pink-100 rounded-lg flex items-center justify-center flex-shrink-0">
											<FileText className="h-8 w-8 text-pink-600" />
										</div>
										
										<div className="flex-1 min-w-0">
											<div className="flex items-center gap-2 mb-1">
												<h3 className="font-semibold text-lg">{post.title}</h3>
												{post.featured && (
													<span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
														Nổi bật
													</span>
												)}
											</div>
											
											<p className="text-muted-foreground text-sm mb-2 line-clamp-2">
												{post.excerpt}
											</p>
											
											<div className="flex items-center gap-4 text-xs text-muted-foreground">
												<div className="flex items-center gap-1">
													<User className="h-3 w-3" />
													{post.author.name}
												</div>
												<div className="flex items-center gap-1">
													<Calendar className="h-3 w-3" />
													{formatDate(post.createdAt)}
												</div>
												<div className="flex items-center gap-1">
													<Eye className="h-3 w-3" />
													{post._count.comments} bình luận
												</div>
											</div>
											
											{post.tags.length > 0 && (
												<div className="flex items-center gap-1 mt-2">
													<TagIcon className="h-3 w-3 text-muted-foreground" />
													<div className="flex gap-1">
														{post.tags.map((tag, index) => (
															<span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
																{tag}
															</span>
														))}
													</div>
												</div>
											)}
										</div>
									</div>

									<div className="flex items-center gap-2 ml-4">
										<span
											className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
												post.status
											)}`}
										>
											{getStatusText(post.status)}
										</span>
										
										<Link href={`/admin/posts/${post.id}/edit`}>
											<Button variant="ghost" size="sm">
												<Edit className="h-4 w-4" />
											</Button>
										</Link>
										
										<Button
											variant="ghost"
											size="sm"
											onClick={() => handleDeletePost(post.id, post.title)}
											className="text-red-600 hover:text-red-700 hover:bg-red-50"
										>
											<Trash2 className="h-4 w-4" />
										</Button>
									</div>
								</div>
							))}
						</div>
					)}
				</CardContent>
			</Card>

			{/* Summary */}
			{!loading && posts.length > 0 && (
				<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
					<Card>
						<CardContent className="p-4">
							<div className="flex items-center gap-2">
								<FileText className="h-5 w-5 text-blue-600" />
								<div>
									<p className="text-sm text-muted-foreground">Tổng bài viết</p>
									<p className="text-2xl font-bold">{posts.length}</p>
								</div>
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardContent className="p-4">
							<div className="flex items-center gap-2">
								<Eye className="h-5 w-5 text-green-600" />
								<div>
									<p className="text-sm text-muted-foreground">Đã xuất bản</p>
									<p className="text-2xl font-bold">
										{posts.filter(p => p.status === 'PUBLISHED').length}
									</p>
								</div>
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardContent className="p-4">
							<div className="flex items-center gap-2">
								<Edit className="h-5 w-5 text-yellow-600" />
								<div>
									<p className="text-sm text-muted-foreground">Bản nháp</p>
									<p className="text-2xl font-bold">
										{posts.filter(p => p.status === 'DRAFT').length}
									</p>
								</div>
							</div>
						</CardContent>
					</Card>

					<Card>
						<CardContent className="p-4">
							<div className="flex items-center gap-2">
								<TagIcon className="h-5 w-5 text-purple-600" />
								<div>
									<p className="text-sm text-muted-foreground">Nổi bật</p>
									<p className="text-2xl font-bold">
										{posts.filter(p => p.featured).length}
									</p>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>
			)}
		</div>
	);
}
