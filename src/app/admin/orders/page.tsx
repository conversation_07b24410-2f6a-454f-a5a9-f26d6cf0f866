'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
	Search, 
	Filter, 
	Eye,
	Package,
	Calendar,
	User,
	CreditCard
} from 'lucide-react';
import { toast } from 'sonner';

interface Order {
	id: string;
	total: number;
	status: string;
	paymentMethod: string;
	paymentStatus: string;
	createdAt: string;
	user: {
		id: string;
		name: string;
		email: string;
	};
	items: Array<{
		id: string;
		quantity: number;
		price: number;
		product: {
			id: string;
			name: string;
			images: string[];
		};
	}>;
	shippingAddress: any;
}

export default function AdminOrdersPage() {
	const [orders, setOrders] = useState<Order[]>([]);
	const [loading, setLoading] = useState(true);
	const [filters, setFilters] = useState({
		search: '',
		status: '',
		paymentMethod: '',
		paymentStatus: '',
	});
	const [pagination, setPagination] = useState({
		page: 1,
		limit: 20,
		total: 0,
		pages: 0,
	});

	// Fetch orders
	const fetchOrders = async () => {
		setLoading(true);
		try {
			const params = new URLSearchParams({
				page: pagination.page.toString(),
				limit: pagination.limit.toString(),
				...(filters.search && { search: filters.search }),
				...(filters.status && { status: filters.status }),
				...(filters.paymentMethod && { paymentMethod: filters.paymentMethod }),
				...(filters.paymentStatus && { paymentStatus: filters.paymentStatus }),
			});

			const response = await fetch(`/api/admin/orders?${params}`);
			const data = await response.json();

			if (response.ok) {
				setOrders(data.orders);
				setPagination(data.pagination);
			} else {
				toast.error('Có lỗi xảy ra khi tải đơn hàng');
			}
		} catch (error) {
			toast.error('Có lỗi xảy ra khi tải đơn hàng');
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchOrders();
	}, [filters, pagination.page]);

	const handleSearch = (e: React.FormEvent) => {
		e.preventDefault();
		setPagination({ ...pagination, page: 1 });
		fetchOrders();
	};

	const handleFilterChange = (key: string, value: string) => {
		setFilters({ ...filters, [key]: value });
		setPagination({ ...pagination, page: 1 });
	};

	const handleUpdateOrderStatus = async (orderId: string, newStatus: string) => {
		try {
			const response = await fetch(`/api/admin/orders/${orderId}`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ status: newStatus }),
			});

			if (response.ok) {
				toast.success('Cập nhật trạng thái đơn hàng thành công');
				fetchOrders();
			} else {
				const data = await response.json();
				toast.error(data.error || 'Có lỗi xảy ra khi cập nhật đơn hàng');
			}
		} catch (error) {
			toast.error('Có lỗi xảy ra khi cập nhật đơn hàng');
		}
	};

	const formatPrice = (price: number) => {
		return new Intl.NumberFormat('vi-VN', {
			style: 'currency',
			currency: 'VND',
		}).format(price);
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('vi-VN', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
		});
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'PENDING':
				return 'text-yellow-600 bg-yellow-100';
			case 'CONFIRMED':
				return 'text-blue-600 bg-blue-100';
			case 'PROCESSING':
				return 'text-purple-600 bg-purple-100';
			case 'SHIPPED':
				return 'text-orange-600 bg-orange-100';
			case 'DELIVERED':
				return 'text-green-600 bg-green-100';
			case 'CANCELLED':
				return 'text-red-600 bg-red-100';
			default:
				return 'text-gray-600 bg-gray-100';
		}
	};

	const getStatusText = (status: string) => {
		switch (status) {
			case 'PENDING':
				return 'Chờ xác nhận';
			case 'CONFIRMED':
				return 'Đã xác nhận';
			case 'PROCESSING':
				return 'Đang xử lý';
			case 'SHIPPED':
				return 'Đang giao hàng';
			case 'DELIVERED':
				return 'Đã giao hàng';
			case 'CANCELLED':
				return 'Đã hủy';
			default:
				return status;
		}
	};

	const getPaymentMethodText = (method: string) => {
		switch (method) {
			case 'COD':
				return 'COD';
			case 'BANK_TRANSFER':
				return 'Chuyển khoản';
			case 'CREDIT_CARD':
				return 'Thẻ tín dụng';
			default:
				return method;
		}
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div>
				<h1 className="text-3xl font-bold">Quản lý đơn hàng</h1>
				<p className="text-muted-foreground">
					Quản lý và theo dõi tất cả đơn hàng
				</p>
			</div>

			{/* Filters */}
			<Card>
				<CardHeader>
					<CardTitle>Bộ lọc</CardTitle>
				</CardHeader>
				<CardContent>
					<form onSubmit={handleSearch} className="space-y-4">
						<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
							{/* Search */}
							<div className="relative">
								<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
								<input
									type="text"
									placeholder="Tìm kiếm đơn hàng, khách hàng..."
									value={filters.search}
									onChange={(e) => setFilters({ ...filters, search: e.target.value })}
									className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
								/>
							</div>

							{/* Status Filter */}
							<select
								value={filters.status}
								onChange={(e) => handleFilterChange('status', e.target.value)}
								className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
							>
								<option value="">Tất cả trạng thái</option>
								<option value="PENDING">Chờ xác nhận</option>
								<option value="CONFIRMED">Đã xác nhận</option>
								<option value="PROCESSING">Đang xử lý</option>
								<option value="SHIPPED">Đang giao hàng</option>
								<option value="DELIVERED">Đã giao hàng</option>
								<option value="CANCELLED">Đã hủy</option>
							</select>

							{/* Payment Method Filter */}
							<select
								value={filters.paymentMethod}
								onChange={(e) => handleFilterChange('paymentMethod', e.target.value)}
								className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
							>
								<option value="">Tất cả phương thức</option>
								<option value="COD">COD</option>
								<option value="BANK_TRANSFER">Chuyển khoản</option>
								<option value="CREDIT_CARD">Thẻ tín dụng</option>
							</select>

							{/* Payment Status Filter */}
							<select
								value={filters.paymentStatus}
								onChange={(e) => handleFilterChange('paymentStatus', e.target.value)}
								className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
							>
								<option value="">Tất cả thanh toán</option>
								<option value="PENDING">Chờ thanh toán</option>
								<option value="PAID">Đã thanh toán</option>
								<option value="FAILED">Thất bại</option>
							</select>
						</div>

						<Button type="submit" className="bg-pink-600 hover:bg-pink-700">
							<Filter className="h-4 w-4 mr-2" />
							Lọc
						</Button>
					</form>
				</CardContent>
			</Card>

			{/* Orders Table */}
			<Card>
				<CardHeader>
					<CardTitle>
						Danh sách đơn hàng ({pagination.total})
					</CardTitle>
				</CardHeader>
				<CardContent>
					{loading ? (
						<div className="space-y-4">
							{Array.from({ length: 5 }, (_, i) => (
								<div key={i} className="animate-pulse">
									<div className="flex items-center space-x-4">
										<div className="w-16 h-16 bg-gray-200 rounded" />
										<div className="flex-1 space-y-2">
											<div className="h-4 bg-gray-200 rounded" />
											<div className="h-4 bg-gray-200 rounded w-2/3" />
										</div>
									</div>
								</div>
							))}
						</div>
					) : orders.length === 0 ? (
						<div className="text-center py-12">
							<Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
							<h3 className="text-lg font-semibold mb-2">Không có đơn hàng nào</h3>
							<p className="text-muted-foreground">
								Chưa có đơn hàng nào được tạo
							</p>
						</div>
					) : (
						<div className="overflow-x-auto">
							<table className="w-full">
								<thead>
									<tr className="border-b">
										<th className="text-left py-3 px-4">Đơn hàng</th>
										<th className="text-left py-3 px-4">Khách hàng</th>
										<th className="text-left py-3 px-4">Sản phẩm</th>
										<th className="text-left py-3 px-4">Tổng tiền</th>
										<th className="text-left py-3 px-4">Thanh toán</th>
										<th className="text-left py-3 px-4">Trạng thái</th>
										<th className="text-left py-3 px-4">Ngày tạo</th>
										<th className="text-left py-3 px-4">Thao tác</th>
									</tr>
								</thead>
								<tbody>
									{orders.map((order) => (
										<tr key={order.id} className="border-b hover:bg-gray-50">
											<td className="py-3 px-4">
												<div>
													<p className="font-medium">#{order.id.slice(-8).toUpperCase()}</p>
													<p className="text-sm text-muted-foreground">
														{order.items.length} sản phẩm
													</p>
												</div>
											</td>
											<td className="py-3 px-4">
												<div>
													<p className="font-medium">{order.user.name}</p>
													<p className="text-sm text-muted-foreground">{order.user.email}</p>
												</div>
											</td>
											<td className="py-3 px-4">
												<div className="flex -space-x-2">
													{order.items.slice(0, 3).map((item, index) => (
														<div
															key={item.id}
															className="relative w-8 h-8 rounded border-2 border-white overflow-hidden"
														>
															<Image
																src={item.product.images[0] || '/images/placeholder.jpg'}
																alt={item.product.name}
																fill
																className="object-cover"
															/>
														</div>
													))}
													{order.items.length > 3 && (
														<div className="w-8 h-8 rounded border-2 border-white bg-gray-100 flex items-center justify-center text-xs">
															+{order.items.length - 3}
														</div>
													)}
												</div>
											</td>
											<td className="py-3 px-4">
												<p className="font-semibold">{formatPrice(order.total)}</p>
											</td>
											<td className="py-3 px-4">
												<div>
													<p className="text-sm">{getPaymentMethodText(order.paymentMethod)}</p>
													<span
														className={`text-xs px-2 py-1 rounded ${
															order.paymentStatus === 'PAID'
																? 'bg-green-100 text-green-800'
																: order.paymentStatus === 'FAILED'
																? 'bg-red-100 text-red-800'
																: 'bg-yellow-100 text-yellow-800'
														}`}
													>
														{order.paymentStatus === 'PAID' ? 'Đã thanh toán' :
														 order.paymentStatus === 'FAILED' ? 'Thất bại' : 'Chờ thanh toán'}
													</span>
												</div>
											</td>
											<td className="py-3 px-4">
												<select
													value={order.status}
													onChange={(e) => handleUpdateOrderStatus(order.id, e.target.value)}
													className={`text-xs px-2 py-1 rounded border-0 ${getStatusColor(order.status)}`}
												>
													<option value="PENDING">Chờ xác nhận</option>
													<option value="CONFIRMED">Đã xác nhận</option>
													<option value="PROCESSING">Đang xử lý</option>
													<option value="SHIPPED">Đang giao hàng</option>
													<option value="DELIVERED">Đã giao hàng</option>
													<option value="CANCELLED">Đã hủy</option>
												</select>
											</td>
											<td className="py-3 px-4 text-sm text-muted-foreground">
												{formatDate(order.createdAt)}
											</td>
											<td className="py-3 px-4">
												<Link href={`/admin/orders/${order.id}`}>
													<Button variant="ghost" size="sm">
														<Eye className="h-4 w-4" />
													</Button>
												</Link>
											</td>
										</tr>
									))}
								</tbody>
							</table>
						</div>
					)}

					{/* Pagination */}
					{pagination.pages > 1 && (
						<div className="flex justify-center items-center gap-2 mt-6">
							<Button
								variant="outline"
								disabled={pagination.page === 1}
								onClick={() => setPagination({ ...pagination, page: pagination.page - 1 })}
							>
								Trước
							</Button>

							{Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
								const page = i + 1;
								return (
									<Button
										key={page}
										variant={pagination.page === page ? 'default' : 'outline'}
										onClick={() => setPagination({ ...pagination, page })}
									>
										{page}
									</Button>
								);
							})}

							<Button
								variant="outline"
								disabled={pagination.page === pagination.pages}
								onClick={() => setPagination({ ...pagination, page: pagination.page + 1 })}
							>
								Sau
							</Button>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
