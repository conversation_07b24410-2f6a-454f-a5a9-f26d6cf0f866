version: "3.8"

services:
  postgres:
    image: postgres:15
    container_name: ns-shop-postgres
    environment:
      POSTGRES_DB: ns_shop
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5499:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: ns-shop-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
