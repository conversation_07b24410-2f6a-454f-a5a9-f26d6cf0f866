const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function testAuth() {
  try {
    console.log('🔐 Testing authentication...');
    
    // Test user credentials
    const email = '<EMAIL>';
    const password = 'user123';
    
    // Find user
    const user = await prisma.user.findUnique({
      where: { email },
    });
    
    if (!user) {
      console.log('❌ User not found');
      return;
    }
    
    console.log('✅ User found:', user.name, user.id);
    
    // Test password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    console.log('🔑 Password valid:', isPasswordValid);
    
    if (isPasswordValid) {
      // Test cart creation
      console.log('🛒 Testing cart creation...');
      
      // Check if cart exists
      let cart = await prisma.cart.findUnique({
        where: { userId: user.id },
      });
      
      if (cart) {
        console.log('✅ Cart already exists:', cart.id);
      } else {
        // Create cart
        cart = await prisma.cart.create({
          data: { userId: user.id },
        });
        console.log('✅ Cart created:', cart.id);
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAuth();
