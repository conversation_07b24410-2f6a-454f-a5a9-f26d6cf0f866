const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDatabase() {
  try {
    console.log('🔍 Checking database...');
    
    // Check users
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
      }
    });
    
    console.log('👥 Users in database:', users.length);
    users.forEach(user => {
      console.log(`  - ${user.name} (${user.email}) - ${user.role} - ID: ${user.id}`);
    });
    
    // Check carts
    const carts = await prisma.cart.findMany({
      include: {
        user: {
          select: {
            email: true,
            name: true,
          }
        }
      }
    });
    
    console.log('\n🛒 Carts in database:', carts.length);
    carts.forEach(cart => {
      console.log(`  - Cart ${cart.id} for user ${cart.user.name} (${cart.user.email})`);
    });
    
    // Check products
    const products = await prisma.product.count();
    console.log('\n📦 Products in database:', products);
    
  } catch (error) {
    console.error('❌ Error checking database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase();
