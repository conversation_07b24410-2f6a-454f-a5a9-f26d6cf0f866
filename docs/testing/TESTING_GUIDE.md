# Hướng Dẫn Testing Toàn Diện - NS Shop

## 📋 Tổng Quan

Tài liệu này cung cấp hướng dẫn chi tiết về việc viết và chạy tests cho dự án NS Shop. Chúng ta sử dụng một hệ thống testing toàn diện bao gồm Unit Testing, Integration Testing, Form Testing, Hook Testing và Mock Testing.

## 🛠️ Công Cụ Testing

### Framework Chính
- **Jest**: Test runner và assertion library
- **React Testing Library**: Testing utilities cho React components
- **MSW (Mock Service Worker)**: API mocking
- **@testing-library/jest-dom**: Custom Jest matchers
- **@testing-library/user-event**: User interaction simulation

### Cấu <PERSON>
```json
{
  "testEnvironment": "jsdom",
  "setupFilesAfterEnv": ["<rootDir>/tests/setup.ts"],
  "testMatch": ["<rootDir>/tests/**/*.test.{ts,tsx}"],
  "collectCoverageFrom": [
    "src/**/*.{ts,tsx}",
    "!src/**/*.d.ts",
    "!src/**/*.stories.{ts,tsx}"
  ],
  "coverageThreshold": {
    "global": {
      "statements": 80,
      "branches": 70,
      "functions": 70,
      "lines": 80
    }
  }
}
```

## 📁 Cấu Trúc Testing

```
tests/
├── unit/                    # Unit tests
│   ├── components/         # Component tests
│   │   ├── ui/            # UI component tests
│   │   ├── admin/         # Admin component tests
│   │   ├── auth/          # Authentication component tests
│   │   └── layout/        # Layout component tests
│   ├── hooks/             # Custom hook tests
│   ├── lib/               # Utility function tests
│   └── contexts/          # Context tests
├── integration/            # Integration tests
│   ├── api/               # API route tests
│   └── database/          # Database integration tests
├── e2e/                   # End-to-end tests (future)
├── mocks/                 # Mock definitions
│   ├── handlers.ts        # MSW handlers
│   └── server.ts          # MSW server setup
├── fixtures/              # Test data
├── helpers/               # Test utilities
└── setup.ts              # Global test setup
```

## 🧪 Loại Tests

### 1. Unit Testing

#### Component Testing
```typescript
// tests/unit/components/ui/button.test.tsx
import { render, screen, fireEvent } from "../../../helpers/test-utils";
import { Button } from "@/components/ui/button";

describe("Button Component", () => {
  it("should render with correct text", () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole("button")).toHaveTextContent("Click me");
  });

  it("should handle click events", () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole("button"));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it("should apply variant classes correctly", () => {
    render(<Button variant="destructive">Delete</Button>);
    expect(screen.getByRole("button")).toHaveClass("bg-destructive");
  });
});
```

#### Hook Testing
```typescript
// tests/unit/hooks/useCart.test.ts
import { renderHook, act } from "@testing-library/react";
import { useCart } from "@/contexts/cart-context";

describe("useCart Hook", () => {
  it("should add items to cart", () => {
    const { result } = renderHook(() => useCart());
    
    act(() => {
      result.current.addToCart(mockProduct, 2);
    });

    expect(result.current.cartItems).toHaveLength(1);
    expect(result.current.getCartItemsCount()).toBe(2);
  });
});
```

### 2. Integration Testing

#### API Route Testing
```typescript
// tests/integration/api/products.test.ts
import { NextRequest } from "next/server";
import { GET } from "@/app/api/products/route";

describe("Products API", () => {
  it("should fetch products with pagination", async () => {
    const request = new NextRequest("http://localhost/api/products?page=1");
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.products).toBeDefined();
    expect(data.pagination).toBeDefined();
  });
});
```

### 3. Form Testing

#### Authentication Forms
```typescript
// tests/unit/components/auth/signin-form.test.tsx
describe("SignIn Form", () => {
  it("should submit form with valid data", async () => {
    render(<SignInForm />);
    
    fireEvent.change(screen.getByLabelText("Email"), {
      target: { value: "<EMAIL>" }
    });
    fireEvent.change(screen.getByLabelText("Password"), {
      target: { value: "password123" }
    });
    
    fireEvent.click(screen.getByRole("button", { name: /đăng nhập/i }));
    
    await waitFor(() => {
      expect(mockSignIn).toHaveBeenCalledWith("credentials", {
        email: "<EMAIL>",
        password: "password123",
        redirect: false,
      });
    });
  });
});
```

### 4. Mock Testing với MSW

#### Setup MSW Handlers
```typescript
// tests/mocks/handlers.ts
import { http, HttpResponse } from "msw";

export const handlers = [
  http.get("/api/products", ({ request }) => {
    const url = new URL(request.url);
    const page = url.searchParams.get("page") || "1";
    
    return HttpResponse.json({
      products: mockProducts,
      pagination: { page: parseInt(page), total: 100 }
    });
  }),
  
  http.post("/api/auth/register", async ({ request }) => {
    const body = await request.json();
    
    if (!body.email) {
      return HttpResponse.json(
        { error: "Email is required" },
        { status: 400 }
      );
    }
    
    return HttpResponse.json(
      { message: "User created successfully" },
      { status: 201 }
    );
  }),
];
```

## 🎯 Best Practices

### 1. Test Structure (AAA Pattern)
```typescript
describe("Component/Function Name", () => {
  it("should do something when condition", () => {
    // Arrange - Setup test data and mocks
    const mockData = { id: 1, name: "Test" };
    const mockFn = jest.fn();
    
    // Act - Execute the code under test
    render(<Component data={mockData} onClick={mockFn} />);
    fireEvent.click(screen.getByRole("button"));
    
    // Assert - Verify the results
    expect(mockFn).toHaveBeenCalledWith(mockData);
  });
});
```

### 2. Test Naming Convention
- **Describe blocks**: Tên component/function/feature
- **Test cases**: "should [expected behavior] when [condition]"
- **Tiếng Việt**: Sử dụng cho user-facing text và error messages

### 3. Mock Strategy
```typescript
// Mock external dependencies
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    pathname: "/test",
  }),
}));

// Mock API calls với MSW
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());
```

### 4. Accessibility Testing
```typescript
it("should be accessible", () => {
  render(<Button>Click me</Button>);
  
  const button = screen.getByRole("button");
  expect(button).toHaveAccessibleName("Click me");
  expect(button).not.toHaveAttribute("aria-disabled");
});
```

## 🚀 Chạy Tests

### Commands Cơ Bản
```bash
# Chạy tất cả tests
npm test

# Chạy tests với coverage
npm run test:coverage

# Chạy tests ở watch mode
npm run test:watch

# Chạy specific test file
npm test -- button.test.tsx

# Chạy tests với pattern
npm test -- --testNamePattern="should render"
```

### Coverage Reports
```bash
# Generate coverage report
npm run test:coverage

# View coverage in browser
open coverage/lcov-report/index.html
```

## 📊 Coverage Targets

| Metric | Target | Current |
|--------|--------|---------|
| Statements | 80% | 5.4% |
| Branches | 70% | 4.56% |
| Functions | 70% | 7.5% |
| Lines | 80% | 5.51% |

## 🔧 Debugging Tests

### Common Issues
1. **Component not found**: Kiểm tra import paths và mock setup
2. **Async operations**: Sử dụng `waitFor` và `findBy` queries
3. **State updates**: Wrap trong `act()` khi cần thiết
4. **Mock not working**: Verify mock placement và hoisting

### Debug Tools
```typescript
// Debug rendered component
import { screen } from "@testing-library/react";
screen.debug(); // Print current DOM

// Debug specific element
screen.debug(screen.getByRole("button"));

// Check what queries are available
screen.logTestingPlaygroundURL();
```

## 📝 Writing New Tests

### Checklist
- [ ] Test covers happy path
- [ ] Test covers error cases
- [ ] Test covers edge cases
- [ ] Proper mocking of dependencies
- [ ] Accessibility considerations
- [ ] Performance considerations
- [ ] Documentation updated

### Template
```typescript
/**
 * [Component/Function] Tests
 * Mô tả ngắn gọn về những gì được test
 */

import { render, screen } from "../helpers/test-utils";
import { ComponentName } from "@/components/path";

// Mocks
jest.mock("dependency", () => ({
  // Mock implementation
}));

describe("ComponentName", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Rendering", () => {
    it("should render correctly", () => {
      // Test implementation
    });
  });

  describe("Interactions", () => {
    it("should handle user interactions", () => {
      // Test implementation
    });
  });

  describe("Error Handling", () => {
    it("should handle errors gracefully", () => {
      // Test implementation
    });
  });
});
```

## 🎓 Tài Liệu Tham Khảo

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [MSW Documentation](https://mswjs.io/docs/)
- [Testing Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)

---

*Tài liệu được cập nhật: 2025-07-02*
*Phiên bản: 1.0.0*
