import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Bắt đầu seed database...');

  // Tạo admin user
  const adminPassword = await bcrypt.hash('admin123', 12);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'NS Shop Admin',
      password: adminPassword,
      role: 'ADMIN',
    },
  });

  // Tạo demo user
  const userPassword = await bcrypt.hash('user123', 12);
  const demoUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      password: userPassword,
      role: 'USER',
      phone: '0123456789',
    },
  });

  // Tạo categories
  const categories = [
    {
      name: 'Thờ<PERSON> trang nữ',
      slug: 'thoi-trang-nu',
      description: '<PERSON><PERSON><PERSON><PERSON>, ph<PERSON> ki<PERSON>n thời trang d<PERSON>nh cho nữ',
      children: [
        { name: '<PERSON>o', slug: 'ao-nu' },
        { name: 'Quần', slug: 'quan-nu' },
        { name: 'V<PERSON>y', slug: 'vay' },
        { name: '<PERSON><PERSON>m', slug: 'dam' },
        { name: 'Phụ kiện', slug: 'phu-kien-nu' },
      ],
    },
    {
      name: 'Thời trang nam',
      slug: 'thoi-trang-nam',
      description: 'Quần áo, phụ kiện thời trang dành cho nam',
      children: [
        { name: 'Áo', slug: 'ao-nam' },
        { name: 'Quần', slug: 'quan-nam' },
        { name: 'Phụ kiện', slug: 'phu-kien-nam' },
      ],
    },
    {
      name: 'Giày dép',
      slug: 'giay-dep',
      description: 'Giày, dép thời trang',
      children: [
        { name: 'Giày nữ', slug: 'giay-nu' },
        { name: 'Giày nam', slug: 'giay-nam' },
        { name: 'Dép', slug: 'dep' },
      ],
    },
    {
      name: 'Túi xách',
      slug: 'tui-xach',
      description: 'Túi xách, balo thời trang',
      children: [
        { name: 'Túi xách nữ', slug: 'tui-xach-nu' },
        { name: 'Túi xách nam', slug: 'tui-xach-nam' },
        { name: 'Balo', slug: 'balo' },
      ],
    },
  ];

  const createdCategories = [];
  for (const category of categories) {
    const parentCategory = await prisma.category.upsert({
      where: { slug: category.slug },
      update: {},
      create: {
        name: category.name,
        slug: category.slug,
        description: category.description,
      },
    });

    createdCategories.push(parentCategory);

    // Tạo subcategories
    for (const child of category.children) {
      await prisma.category.upsert({
        where: { slug: child.slug },
        update: {},
        create: {
          name: child.name,
          slug: child.slug,
          parentId: parentCategory.id,
        },
      });
    }
  }

  // Tạo products
  const products = [
    // Thời trang nữ - Áo
    {
      name: 'Áo Blouse Hoa Nhí Vintage',
      slug: 'ao-blouse-hoa-nhi-vintage',
      description: 'Áo blouse nữ họa tiết hoa nhí vintage, chất liệu voan mềm mại, phù hợp đi làm và dạo phố.',
      price: 299000,
      salePrice: 249000,
      sku: 'BL001',
      stock: 50,
      categorySlug: 'ao-nu',
      images: [
        'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500',
        'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500',
      ],
      featured: true,
    },
    {
      name: 'Áo Sơ Mi Trắng Basic',
      slug: 'ao-so-mi-trang-basic',
      description: 'Áo sơ mi trắng basic, thiết kế đơn giản, thanh lịch, phù hợp mọi dịp.',
      price: 199000,
      sku: 'SM001',
      stock: 30,
      categorySlug: 'ao-nu',
      images: [
        'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=500',
      ],
    },
    {
      name: 'Áo Thun Crop Top',
      slug: 'ao-thun-crop-top',
      description: 'Áo thun crop top năng động, chất cotton mềm mại, phù hợp với quần jeans.',
      price: 149000,
      salePrice: 119000,
      sku: 'CT001',
      stock: 40,
      categorySlug: 'ao-nu',
      images: [
        'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500',
      ],
    },

    // Thời trang nữ - Váy
    {
      name: 'Váy Midi Hoa Cúc',
      slug: 'vay-midi-hoa-cuc',
      description: 'Váy midi họa tiết hoa cúc dễ thương, thiết kế xòe nhẹ, phù hợp dạo phố.',
      price: 399000,
      salePrice: 329000,
      sku: 'VM001',
      stock: 25,
      categorySlug: 'vay',
      images: [
        'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=500',
      ],
      featured: true,
    },
    {
      name: 'Váy Denim Ngắn',
      slug: 'vay-denim-ngan',
      description: 'Váy denim ngắn phong cách trẻ trung, có túi và nút cài phía trước.',
      price: 259000,
      sku: 'VD001',
      stock: 35,
      categorySlug: 'vay',
      images: [
        'https://images.unsplash.com/photo-1583496661160-fb5886a13d27?w=500',
      ],
    },

    // Thời trang nam - Áo
    {
      name: 'Áo Polo Nam Classic',
      slug: 'ao-polo-nam-classic',
      description: 'Áo polo nam classic, chất cotton cao cấp, thiết kế lịch lãm.',
      price: 299000,
      sku: 'PL001',
      stock: 45,
      categorySlug: 'ao-nam',
      images: [
        'https://images.unsplash.com/photo-1586790170083-2f9ceadc732d?w=500',
      ],
    },
    {
      name: 'Áo Thun Nam Oversize',
      slug: 'ao-thun-nam-oversize',
      description: 'Áo thun nam form oversize trendy, chất cotton 100%, thoải mái.',
      price: 179000,
      salePrice: 149000,
      sku: 'TN001',
      stock: 60,
      categorySlug: 'ao-nam',
      images: [
        'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500',
      ],
      featured: true,
    },

    // Giày dép
    {
      name: 'Giày Sneaker Nữ Trắng',
      slug: 'giay-sneaker-nu-trang',
      description: 'Giày sneaker nữ màu trắng basic, phù hợp mọi outfit, đế cao su êm ái.',
      price: 599000,
      salePrice: 499000,
      sku: 'SN001',
      stock: 20,
      categorySlug: 'giay-nu',
      images: [
        'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=500',
      ],
      featured: true,
    },
    {
      name: 'Giày Boot Nữ Da',
      slug: 'giay-boot-nu-da',
      description: 'Giày boot nữ da thật, thiết kế cổ điển, phù hợp mùa đông.',
      price: 899000,
      sku: 'BT001',
      stock: 15,
      categorySlug: 'giay-nu',
      images: [
        'https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=500',
      ],
    },

    // Túi xách
    {
      name: 'Túi Xách Tay Nữ Da',
      slug: 'tui-xach-tay-nu-da',
      description: 'Túi xách tay nữ da cao cấp, thiết kế sang trọng, nhiều ngăn tiện lợi.',
      price: 1299000,
      salePrice: 999000,
      sku: 'TX001',
      stock: 12,
      categorySlug: 'tui-xach-nu',
      images: [
        'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=500',
      ],
      featured: true,
    },
    {
      name: 'Balo Laptop Unisex',
      slug: 'balo-laptop-unisex',
      description: 'Balo laptop unisex, chống nước, nhiều ngăn, phù hợp đi học và làm việc.',
      price: 799000,
      sku: 'BL002',
      stock: 30,
      categorySlug: 'balo',
      images: [
        'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=500',
      ],
    },
  ];

  // Tạo products
  for (const productData of products) {
    const category = await prisma.category.findUnique({
      where: { slug: productData.categorySlug },
    });

    if (category) {
      const product = await prisma.product.upsert({
        where: { slug: productData.slug },
        update: {},
        create: {
          name: productData.name,
          slug: productData.slug,
          description: productData.description,
          price: productData.price,
          salePrice: productData.salePrice,
          sku: productData.sku,
          stock: productData.stock,
          categoryId: category.id,
          images: productData.images,
          featured: productData.featured || false,
          status: 'ACTIVE',
        },
      });

      console.log(`✅ Tạo sản phẩm: ${product.name}`);
    }
  }

  // Tạo sample reviews
  const products_for_review = await prisma.product.findMany({ take: 5 });
  
  for (const product of products_for_review) {
    // Tạo 2-3 reviews cho mỗi sản phẩm
    const reviewsData = [
      {
        rating: 5,
        comment: 'Sản phẩm rất đẹp và chất lượng tốt. Giao hàng nhanh, đóng gói cẩn thận.',
        userId: demoUser.id,
      },
      {
        rating: 4,
        comment: 'Sản phẩm ổn, đúng như mô tả. Sẽ mua lại lần sau.',
        userId: admin.id,
      },
    ];

    for (const reviewData of reviewsData) {
      await prisma.review.create({
        data: {
          ...reviewData,
          productId: product.id,
        },
      });
    }

    // Cập nhật avgRating và reviewCount
    const reviews = await prisma.review.findMany({
      where: { productId: product.id },
    });

    const avgRating = reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length;

    await prisma.product.update({
      where: { id: product.id },
      data: {
        avgRating: Math.round(avgRating * 10) / 10,
        reviewCount: reviews.length,
      },
    });
  }

  console.log('✅ Seed database hoàn thành!');
  console.log('📧 Admin: <EMAIL> / admin123');
  console.log('👤 User: <EMAIL> / user123');
}

main()
  .catch((e) => {
    console.error('❌ Lỗi seed database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
