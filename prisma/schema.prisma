// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model
model User {
  id          String    @id @default(cuid())
  email       String    @unique
  name        String
  password    String
  role        Role      @default(USER)
  avatar      String?
  phone       String?
  dateOfBirth DateTime?
  gender      Gender?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  orders    Order[]
  addresses Address[]
  reviews   Review[]
  cart      Cart?

  @@map("users")
}

// Category model
model Category {
  id          String   @id @default(cuid())
  name        String
  description String?
  slug        String   @unique
  image       String?
  parentId    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  products Product[]

  @@map("categories")
}

// Product model
model Product {
  id          String        @id @default(cuid())
  name        String
  description String
  price       Float
  salePrice   Float?
  images      String[]
  categoryId  String
  stock       Int           @default(0)
  sku         String        @unique
  slug        String        @unique
  featured    Boolean       @default(false)
  status      ProductStatus @default(ACTIVE)
  tags        String[]
  avgRating   Float         @default(0)
  reviewCount Int           @default(0)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  category  Category    @relation(fields: [categoryId], references: [id])
  cartItems CartItem[]
  orderItems OrderItem[]
  reviews   Review[]

  @@map("products")
}

// Cart model
model Cart {
  id        String   @id @default(cuid())
  userId    String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user  User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  items CartItem[]

  @@map("carts")
}

// Cart Item model
model CartItem {
  id        String   @id @default(cuid())
  cartId    String
  productId String
  quantity  Int
  price     Float
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  cart    Cart    @relation(fields: [cartId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([cartId, productId])
  @@map("cart_items")
}

// Order model
model Order {
  id              String        @id @default(cuid())
  userId          String
  total           Float
  status          OrderStatus   @default(PENDING)
  paymentMethod   PaymentMethod @default(COD)
  paymentStatus   PaymentStatus @default(PENDING)
  shippingAddress Json
  billingAddress  Json?
  notes           String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  user  User        @relation(fields: [userId], references: [id])
  items OrderItem[]

  @@map("orders")
}

// Order Item model
model OrderItem {
  id        String   @id @default(cuid())
  orderId   String
  productId String
  quantity  Int
  price     Float
  total     Float
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// Address model
model Address {
  id        String   @id @default(cuid())
  userId    String
  fullName  String
  phone     String
  address   String
  ward      String
  district  String
  province  String
  isDefault Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("addresses")
}

// Review model
model Review {
  id        String   @id @default(cuid())
  userId    String
  productId String
  rating    Int      @default(5)
  comment   String?
  images    String[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("reviews")
}

model Setting {
  id    String @id @default(cuid())
  key   String @unique
  value Json
  type  String @default("string") // string, number, boolean, json

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Enums
enum Role {
  USER
  ADMIN
}

enum Gender {
  MALE
  FEMALE
  OTHER
}

enum ProductStatus {
  ACTIVE
  INACTIVE
  OUT_OF_STOCK
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
}

enum PaymentMethod {
  COD
  BANK_TRANSFER
  CREDIT_CARD
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}
