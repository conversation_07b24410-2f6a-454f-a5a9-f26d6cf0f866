import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const DEFAULT_SETTINGS = [
  {
    key: 'siteName',
    value: 'NS Shop',
    type: 'string',
  },
  {
    key: 'siteDescription',
    value: 'C<PERSON><PERSON> hàng thời trang trực tuyến',
    type: 'string',
  },
  {
    key: 'siteUrl',
    value: 'https://nsshop.com',
    type: 'string',
  },
  {
    key: 'logo',
    value: '',
    type: 'string',
  },
  {
    key: 'favicon',
    value: '',
    type: 'string',
  },
  {
    key: 'contactEmail',
    value: '<EMAIL>',
    type: 'string',
  },
  {
    key: 'contactPhone',
    value: '**********',
    type: 'string',
  },
  {
    key: 'address',
    value: '123 Đường ABC, Quận 1, TP.HCM',
    type: 'string',
  },
  {
    key: 'socialMedia',
    value: {
      facebook: '',
      instagram: '',
      twitter: '',
    },
    type: 'json',
  },
  {
    key: 'paymentMethods',
    value: {
      cod: true,
      bankTransfer: true,
      creditCard: false,
    },
    type: 'json',
  },
  {
    key: 'shippingSettings',
    value: {
      freeShippingThreshold: 500000,
      shippingFee: 30000,
      estimatedDelivery: '2-3 ngày',
    },
    type: 'json',
  },
  {
    key: 'emailSettings',
    value: {
      smtpHost: '',
      smtpPort: 587,
      smtpUser: '',
      smtpPassword: '',
      fromEmail: '<EMAIL>',
      fromName: 'NS Shop',
    },
    type: 'json',
  },
  {
    key: 'notifications',
    value: {
      orderNotifications: true,
      stockAlerts: true,
      customerNotifications: true,
    },
    type: 'json',
  },
  {
    key: 'seoSettings',
    value: {
      metaTitle: 'NS Shop - Thời trang trực tuyến',
      metaDescription: 'Cửa hàng thời trang trực tuyến với những sản phẩm chất lượng cao',
      metaKeywords: 'thời trang, quần áo, giày dép, phụ kiện',
      googleAnalytics: '',
      facebookPixel: '',
    },
    type: 'json',
  },
  {
    key: 'securitySettings',
    value: {
      enableTwoFactor: false,
      sessionTimeout: 24,
      maxLoginAttempts: 5,
      enableCaptcha: false,
    },
    type: 'json',
  },
];

async function seedSettings() {
  console.log('🌱 Seeding default settings...');

  try {
    // Delete existing settings
    await prisma.setting.deleteMany();

    // Create default settings
    for (const setting of DEFAULT_SETTINGS) {
      await prisma.setting.create({
        data: setting,
      });
    }

    console.log('✅ Default settings seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding settings:', error);
    throw error;
  }
}

if (require.main === module) {
  seedSettings()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { seedSettings };
