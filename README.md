# NS Shop - Fashion E-commerce Store

Một ứng dụng thương mại điện tử thời trang được xây dựng với Next.js, TypeScript, Tailwind CSS và Prisma.

## ✨ Tính năng

### 🛍️ Shop Frontend
- **Trang chủ hiện đại** với hero section, danh mục sản phẩm và sản phẩm nổi bật
- **Thiết kế responsive** tối ưu cho mọi thiết bị
- **Giao diện thời trang** với gradient và animation mượt mà
- **Tìm kiếm sản phẩm** thông minh
- **Giỏ hàng** và quản lý đơn hàng
- **Đ<PERSON>h giá sản phẩm** từ khách hàng

### 🔧 Admin Dashboard
- **Dashboard tổng quan** với thống kê chi tiết
- **Quản lý sản phẩm** (thêm, sửa, xóa)
- **Quản lý danh mục** với cấu trúc phân cấp
- **Quản lý đơn hàng** và trạng thái giao hàng
- **Quản lý khách hàng**
- **Báo cáo và phân tích** doanh số

### 🎨 UI/UX
- **Dark/Light mode** tự động
- **Tailwind CSS** với custom theme thời trang
- **Framer Motion** cho animation
- **Radix UI** components
- **Responsive design** hoàn hảo

## 🚀 Công nghệ sử dụng

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Database**: PostgreSQL với Prisma ORM
- **Authentication**: NextAuth.js
- **UI Components**: Radix UI
- **Icons**: Lucide React
- **Animation**: Framer Motion
- **Form Handling**: React Hook Form + Zod
- **State Management**: React Context/Zustand

## 📦 Cài đặt

### 1. Clone repository
```bash
git clone https://github.com/your-username/ns-shop.git
cd ns-shop
```

### 2. Cài đặt dependencies
```bash
yarn install
```

### 3. Thiết lập database
```bash
# Khởi động PostgreSQL và Redis
yarn dup

# Copy file environment
cp .env.example .env

# Chạy migration
yarn p:m
```

### 4. Khởi động development server
```bash
yarn dev
```

Ứng dụng sẽ chạy tại `http://localhost:3000`

## 📁 Cấu trúc dự án

```
ns-shop/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── admin/          # Admin dashboard pages
│   │   ├── products/       # Product pages
│   │   ├── categories/     # Category pages
│   │   └── ...
│   ├── components/         # React components
│   │   ├── ui/            # Base UI components
│   │   ├── layout/        # Layout components
│   │   ├── shop/          # Shop-specific components
│   │   └── admin/         # Admin-specific components
│   ├── lib/               # Utility functions
│   ├── types/             # TypeScript type definitions
│   ├── hooks/             # Custom React hooks
│   └── contexts/          # React contexts
├── prisma/                # Database schema and migrations
├── public/                # Static assets
└── ...
```

## 🎯 Scripts

```bash
# Development
yarn dev              # Khởi động dev server
yarn build            # Build production
yarn start            # Khởi động production server

# Database
yarn p:m              # Chạy Prisma migration
yarn p:m:r            # Reset database
yarn p:s              # Mở Prisma Studio

# Docker
yarn dup             # Khởi động PostgreSQL và Redis

# Linting
yarn lint             # Kiểm tra TypeScript
```

## 🌟 Tính năng nổi bật

### Thiết kế thời trang
- Gradient màu sắc hiện đại
- Animation mượt mà với Framer Motion
- Typography và spacing tối ưu
- Glass morphism effects

### Performance
- Next.js 15 với Turbopack
- Image optimization
- Code splitting tự động
- SEO optimization

### Developer Experience
- TypeScript strict mode
- ESLint + Prettier
- Hot reload
- Type-safe database với Prisma

## 🔐 Authentication

Dự án sử dụng NextAuth.js với các provider:
- Email/Password
- Google OAuth (tùy chọn)
- Facebook OAuth (tùy chọn)

## 📊 Database Schema

### Core Models
- **User**: Quản lý người dùng và admin
- **Product**: Sản phẩm với hình ảnh, giá, stock
- **Category**: Danh mục phân cấp
- **Order**: Đơn hàng và trạng thái
- **Cart**: Giỏ hàng của người dùng
- **Review**: Đánh giá sản phẩm

## 🚀 Deployment

### Vercel (Recommended)
```bash
# Deploy to Vercel
vercel --prod
```

### Docker
```bash
# Build Docker image
docker build -t ns-shop .

# Run container
docker run -p 3000:3000 ns-shop
```

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Tạo Pull Request

## 📝 License

Dự án này được phân phối dưới MIT License. Xem file `LICENSE` để biết thêm chi tiết.

## 📞 Liên hệ

- **Email**: <EMAIL>
- **Website**: https://nsshop.com
- **GitHub**: https://github.com/your-username/ns-shop

---

Made with ❤️ by NS Team
