import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function validateData() {
  console.log('🔍 Bắt đầu kiểm tra dữ liệu...\n');

  try {
    // Check Users
    const users = await prisma.user.findMany();
    const adminUsers = users.filter(u => u.role === 'ADMIN');
    const regularUsers = users.filter(u => u.role === 'USER');
    
    console.log('👥 USERS:');
    console.log(`   Total: ${users.length}`);
    console.log(`   Admin: ${adminUsers.length}`);
    console.log(`   Regular: ${regularUsers.length}`);
    
    if (adminUsers.length === 0) {
      console.log('   ⚠️  Cảnh báo: Không có admin user nào!');
    } else {
      console.log(`   ✅ Admin user: ${adminUsers[0].email}`);
    }
    console.log('');

    // Check Categories
    const categories = await prisma.category.findMany({
      include: {
        children: true,
        _count: {
          select: { products: true }
        }
      }
    });
    
    const parentCategories = categories.filter(c => !c.parentId);
    const childCategories = categories.filter(c => c.parentId);
    
    console.log('📂 CATEGORIES:');
    console.log(`   Total: ${categories.length}`);
    console.log(`   Parent: ${parentCategories.length}`);
    console.log(`   Child: ${childCategories.length}`);
    
    parentCategories.forEach(cat => {
      console.log(`   📁 ${cat.name} (${cat._count.products} products)`);
      const children = categories.filter(c => c.parentId === cat.id);
      children.forEach(child => {
        console.log(`      📄 ${child.name} (${child._count.products} products)`);
      });
    });
    console.log('');

    // Check Products
    const products = await prisma.product.findMany({
      include: {
        category: true,
        _count: {
          select: { reviews: true }
        }
      }
    });
    
    const activeProducts = products.filter(p => p.status === 'ACTIVE');
    const featuredProducts = products.filter(p => p.featured);
    const saleProducts = products.filter(p => p.salePrice);
    const outOfStockProducts = products.filter(p => p.stock === 0);
    
    console.log('📦 PRODUCTS:');
    console.log(`   Total: ${products.length}`);
    console.log(`   Active: ${activeProducts.length}`);
    console.log(`   Featured: ${featuredProducts.length}`);
    console.log(`   On Sale: ${saleProducts.length}`);
    console.log(`   Out of Stock: ${outOfStockProducts.length}`);
    
    // Price statistics
    const prices = products.map(p => p.salePrice || p.price);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const avgPrice = prices.reduce((a, b) => a + b, 0) / prices.length;
    
    console.log(`   Price Range: ${minPrice.toLocaleString('vi-VN')}đ - ${maxPrice.toLocaleString('vi-VN')}đ`);
    console.log(`   Average Price: ${Math.round(avgPrice).toLocaleString('vi-VN')}đ`);
    
    // Products without images
    const productsWithoutImages = products.filter(p => !p.images || p.images.length === 0);
    if (productsWithoutImages.length > 0) {
      console.log(`   ⚠️  ${productsWithoutImages.length} products without images`);
    }
    console.log('');

    // Check Reviews
    const reviews = await prisma.review.findMany({
      include: {
        user: true,
        product: true
      }
    });
    
    const ratings = reviews.map(r => r.rating);
    const avgRating = ratings.reduce((a, b) => a + b, 0) / ratings.length;
    
    console.log('⭐ REVIEWS:');
    console.log(`   Total: ${reviews.length}`);
    console.log(`   Average Rating: ${avgRating.toFixed(1)}/5`);
    
    // Rating distribution
    for (let i = 1; i <= 5; i++) {
      const count = reviews.filter(r => r.rating === i).length;
      const percentage = ((count / reviews.length) * 100).toFixed(1);
      console.log(`   ${i} star: ${count} (${percentage}%)`);
    }
    console.log('');

    // Check Orders
    const orders = await prisma.order.findMany({
      include: {
        user: true,
        items: {
          include: {
            product: true
          }
        }
      }
    });
    
    const ordersByStatus = {
      PENDING: orders.filter(o => o.status === 'PENDING').length,
      CONFIRMED: orders.filter(o => o.status === 'CONFIRMED').length,
      PROCESSING: orders.filter(o => o.status === 'PROCESSING').length,
      SHIPPED: orders.filter(o => o.status === 'SHIPPED').length,
      DELIVERED: orders.filter(o => o.status === 'DELIVERED').length,
      CANCELLED: orders.filter(o => o.status === 'CANCELLED').length,
    };
    
    const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);
    const avgOrderValue = orders.length > 0 ? totalRevenue / orders.length : 0;
    
    console.log('🛒 ORDERS:');
    console.log(`   Total: ${orders.length}`);
    console.log(`   Total Revenue: ${totalRevenue.toLocaleString('vi-VN')}đ`);
    console.log(`   Average Order Value: ${Math.round(avgOrderValue).toLocaleString('vi-VN')}đ`);
    
    Object.entries(ordersByStatus).forEach(([status, count]) => {
      if (count > 0) {
        console.log(`   ${status}: ${count}`);
      }
    });
    console.log('');

    // Check Cart
    const carts = await prisma.cart.findMany({
      include: {
        items: {
          include: {
            product: true
          }
        }
      }
    });
    
    const activeCarts = carts.filter(c => c.items.length > 0);
    
    console.log('🛍️ CARTS:');
    console.log(`   Total: ${carts.length}`);
    console.log(`   Active (with items): ${activeCarts.length}`);
    console.log('');

    // Data integrity checks
    console.log('🔍 DATA INTEGRITY CHECKS:');
    
    // Products without categories
    const productsWithoutCategory = products.filter(p => !p.category);
    if (productsWithoutCategory.length > 0) {
      console.log(`   ❌ ${productsWithoutCategory.length} products without category`);
    } else {
      console.log(`   ✅ All products have categories`);
    }
    
    // Orders without items
    const ordersWithoutItems = orders.filter(o => o.items.length === 0);
    if (ordersWithoutItems.length > 0) {
      console.log(`   ❌ ${ordersWithoutItems.length} orders without items`);
    } else {
      console.log(`   ✅ All orders have items`);
    }
    
    // Reviews without users or products
    const orphanReviews = reviews.filter(r => !r.user || !r.product);
    if (orphanReviews.length > 0) {
      console.log(`   ❌ ${orphanReviews.length} orphan reviews`);
    } else {
      console.log(`   ✅ All reviews have valid user and product`);
    }
    
    // Products with incorrect avgRating
    const productsWithWrongRating = [];
    for (const product of products) {
      const productReviews = reviews.filter(r => r.productId === product.id);
      if (productReviews.length > 0) {
        const calculatedAvg = productReviews.reduce((sum, r) => sum + r.rating, 0) / productReviews.length;
        const roundedAvg = Math.round(calculatedAvg * 10) / 10;
        if (Math.abs(product.avgRating - roundedAvg) > 0.1) {
          productsWithWrongRating.push(product);
        }
      }
    }
    
    if (productsWithWrongRating.length > 0) {
      console.log(`   ❌ ${productsWithWrongRating.length} products with incorrect avgRating`);
    } else {
      console.log(`   ✅ All product ratings are correct`);
    }

    console.log('\n✅ Kiểm tra dữ liệu hoàn thành!');
    
    // Summary
    console.log('\n📊 SUMMARY:');
    console.log(`   ${users.length} users, ${categories.length} categories, ${products.length} products`);
    console.log(`   ${reviews.length} reviews, ${orders.length} orders, ${carts.length} carts`);
    console.log(`   Total revenue: ${totalRevenue.toLocaleString('vi-VN')}đ`);

  } catch (error) {
    console.error('❌ Lỗi khi kiểm tra dữ liệu:', error);
  }
}

validateData()
  .catch((e) => {
    console.error('❌ Lỗi:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
