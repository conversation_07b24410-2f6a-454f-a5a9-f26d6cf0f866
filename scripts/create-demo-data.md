# Hướng dẫn tạo dữ liệu mẫu cho NS Shop

## 🚀 Cách chạy scripts

### 1. Cài đặt dependencies
```bash
npm install
```

### 2. Cấu hình database
```bash
# Tạo database và tables
npx prisma db push

# Hoặc nếu đã có database, reset và tạo lại
npx prisma db push --force-reset
```

### 3. Tạo dữ liệu cơ bản
```bash
# Tạo admin user, categories, và một số sản phẩm cơ bản
npm run db:seed
```

### 4. Tạo thêm dữ liệu mẫu
```bash
# Tạo thêm nhiều sản phẩm, users, reviews, orders
npm run db:generate-data
```

### 5. Setup đầy đủ (Recommended)
```bash
# Reset database và tạo tất cả dữ liệu mẫu
npm run db:full-setup
```

## 📊 Dữ liệu được tạo

### Users
- **Admin**: <EMAIL> / admin123
- **Demo User**: <EMAIL> / user123
- **5 Sample Users**: với email và thông tin ngẫu nhiên

### Categories
- **Thời trang nữ**
  - Áo
  - Quần  
  - Váy
  - Đầm
  - Phụ kiện
- **Thời trang nam**
  - Áo
  - Quần
  - Phụ kiện
- **Giày dép**
  - Giày nữ
  - Giày nam
  - Dép
- **Túi xách**
  - Túi xách nữ
  - Túi xách nam
  - Balo

### Products
- **~30 sản phẩm** thời trang đa dạng
- Có giá gốc và giá sale
- Hình ảnh từ Unsplash
- Stock ngẫu nhiên
- Một số sản phẩm featured
- SKU tự động generate

### Reviews
- **2-5 reviews** cho mỗi sản phẩm
- Rating từ 4-5 sao (chủ yếu positive)
- Comments thực tế
- Tự động tính avgRating và reviewCount

### Orders
- **6 đơn hàng mẫu** từ 3 users
- Các trạng thái khác nhau (PENDING, CONFIRMED, DELIVERED)
- Địa chỉ giao hàng và thanh toán
- Order items với quantity và price

## 🛠️ Customization

### Thêm sản phẩm mới
Chỉnh sửa file `scripts/generate-sample-data.ts`:

```typescript
const fashionProducts = [
  {
    name: 'Tên sản phẩm',
    description: 'Mô tả sản phẩm',
    price: 299000,
    salePrice: 249000, // Optional
    category: 'slug-category',
    images: ['https://example.com/image.jpg'],
  },
  // ... thêm sản phẩm khác
];
```

### Thêm categories mới
Chỉnh sửa file `prisma/seed.ts`:

```typescript
const categories = [
  {
    name: 'Tên danh mục',
    slug: 'slug-danh-muc',
    description: 'Mô tả danh mục',
    children: [
      { name: 'Danh mục con', slug: 'slug-con' },
    ],
  },
];
```

### Thêm users mẫu
Chỉnh sửa file `scripts/generate-sample-data.ts`:

```typescript
const sampleUsers = [
  {
    name: 'Họ tên',
    email: '<EMAIL>',
    phone: '0123456789',
  },
];
```

## 🔧 Troubleshooting

### Lỗi database connection
```bash
# Kiểm tra DATABASE_URL trong .env
echo $DATABASE_URL

# Hoặc tạo database mới
npx prisma db push
```

### Lỗi duplicate data
```bash
# Reset database hoàn toàn
npm run db:reset
```

### Lỗi missing dependencies
```bash
# Cài đặt lại dependencies
npm install
```

## 📝 Notes

- Tất cả passwords mặc định là: `admin123` (admin) và `user123` (users)
- Hình ảnh sử dụng Unsplash (cần internet để load)
- Dữ liệu được tạo với locale Việt Nam
- Giá cả tính bằng VND
- Có thể chạy scripts nhiều lần (sử dụng upsert)

## 🎯 Sử dụng cho testing

Sau khi tạo dữ liệu, bạn có thể:

1. **Login admin**: <EMAIL> / admin123
2. **Truy cập admin dashboard**: /admin
3. **Test shopping flow**: Thêm sản phẩm vào cart → Checkout
4. **Test user features**: Đăng ký, đăng nhập, profile, orders
5. **Test reviews**: Đánh giá sản phẩm (cần đăng nhập)

## 🚀 Production Setup

Cho production, chỉ chạy:
```bash
npm run db:seed
```

Không chạy `db:generate-data` trên production vì nó tạo dữ liệu test.
