import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

// Sample data arrays
const fashionProducts = [
  // Á<PERSON> nữ
  {
    name: "<PERSON><PERSON> Nữ Tay Dài Vintage",
    description:
      "<PERSON>o kiểu nữ tay dài phong cách vintage, chất liệu voan mềm mại, thiết kế thanh lịch.",
    price: 350000,
    salePrice: 280000,
    category: "ao-nu",
    images: [
      "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500",
    ],
  },
  {
    name: "<PERSON><PERSON> Cổ Tròn",
    description: "<PERSON><PERSON> len nữ cổ tròn ấm áp, phù hợp mùa đông, nhiều màu sắc.",
    price: 450000,
    category: "ao-nu",
    images: ["https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=500"],
  },
  {
    name: "<PERSON>o <PERSON> Oversize",
    description:
      "Áo hoodie nữ form oversize trendy, chất cotton cao cấp, thoải mái.",
    price: 320000,
    salePrice: 250000,
    category: "ao-nu",
    images: [
      "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500",
    ],
  },

  // Quần nữ
  {
    name: "Quần Jeans Nữ Skinny",
    description:
      "Quần jeans nữ skinny ôm dáng, chất denim cao cấp, form chuẩn.",
    price: 399000,
    category: "quan-nu",
    images: [
      "https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=500",
    ],
  },
  {
    name: "Quần Tây Nữ Công Sở",
    description: "Quần tây nữ công sở thanh lịch, chất liệu cao cấp, form đẹp.",
    price: 550000,
    salePrice: 450000,
    category: "quan-nu",
    images: [
      "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500",
    ],
  },

  // Váy
  {
    name: "Váy Maxi Hoa Nhí",
    description:
      "Váy maxi họa tiết hoa nhí dễ thương, phù hợp dạo phố và du lịch.",
    price: 480000,
    category: "vay",
    images: [
      "https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=500",
    ],
  },
  {
    name: "Váy Xòe Vintage",
    description: "Váy xòe phong cách vintage, thiết kế cổ điển, thanh lịch.",
    price: 420000,
    salePrice: 350000,
    category: "vay",
    images: [
      "https://images.unsplash.com/photo-1583496661160-fb5886a13d27?w=500",
    ],
  },

  // Đầm
  {
    name: "Đầm Dự Tiệc Sang Trọng",
    description:
      "Đầm dự tiệc sang trọng, chất liệu cao cấp, thiết kế quyến rũ.",
    price: 890000,
    salePrice: 750000,
    category: "dam",
    images: [
      "https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=500",
    ],
  },

  // Áo nam
  {
    name: "Áo Sơ Mi Nam Công Sở",
    description:
      "Áo sơ mi nam công sở lịch lãm, chất cotton cao cấp, form chuẩn.",
    price: 380000,
    category: "ao-nam",
    images: [
      "https://images.unsplash.com/photo-1586790170083-2f9ceadc732d?w=500",
    ],
  },
  {
    name: "Áo Khoác Nam Bomber",
    description:
      "Áo khoác nam bomber thời trang, chất liệu dù cao cấp, phong cách trẻ trung.",
    price: 650000,
    salePrice: 520000,
    category: "ao-nam",
    images: [
      "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500",
    ],
  },

  // Quần nam
  {
    name: "Quần Jeans Nam Straight",
    description: "Quần jeans nam straight fit, chất denim cao cấp, bền đẹp.",
    price: 450000,
    category: "quan-nam",
    images: [
      "https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=500",
    ],
  },
  {
    name: "Quần Kaki Nam Slim",
    description:
      "Quần kaki nam slim fit, chất liệu cotton thoáng mát, phù hợp đi làm.",
    price: 350000,
    category: "quan-nam",
    images: [
      "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500",
    ],
  },

  // Giày
  {
    name: "Giày Cao Gót Nữ 7cm",
    description: "Giày cao gót nữ 7cm thanh lịch, da thật, đế êm ái.",
    price: 750000,
    salePrice: 600000,
    category: "giay-nu",
    images: ["https://images.unsplash.com/photo-1549298916-b41d501d3772?w=500"],
  },
  {
    name: "Giày Thể Thao Nam",
    description:
      "Giày thể thao nam năng động, đế cao su chống trượt, thoáng khí.",
    price: 680000,
    category: "giay-nam",
    images: ["https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=500"],
  },
  {
    name: "Dép Sandal Nữ",
    description: "Dép sandal nữ thời trang, quai da mềm, đế êm chân.",
    price: 280000,
    category: "dep",
    images: ["https://images.unsplash.com/photo-1549298916-b41d501d3772?w=500"],
  },

  // Túi xách
  {
    name: "Túi Đeo Chéo Nữ Mini",
    description: "Túi đeo chéo nữ mini xinh xắn, da PU cao cấp, nhiều màu.",
    price: 320000,
    salePrice: 250000,
    category: "tui-xach-nu",
    images: ["https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=500"],
  },
  {
    name: "Túi Công Sở Nam",
    description: "Túi công sở nam lịch lãm, da thật, nhiều ngăn tiện lợi.",
    price: 890000,
    category: "tui-xach-nam",
    images: ["https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=500"],
  },
];

const sampleUsers = [
  {
    name: "Nguyễn Thị Lan",
    email: "<EMAIL>",
    phone: "0987654321",
  },
  {
    name: "Trần Văn Minh",
    email: "<EMAIL>",
    phone: "0912345678",
  },
  {
    name: "Lê Thị Hoa",
    email: "<EMAIL>",
    phone: "0923456789",
  },
  {
    name: "Phạm Văn Đức",
    email: "<EMAIL>",
    phone: "0934567890",
  },
  {
    name: "Hoàng Thị Mai",
    email: "<EMAIL>",
    phone: "0945678901",
  },
];

const reviewComments = [
  "Sản phẩm rất đẹp và chất lượng tốt. Giao hàng nhanh, đóng gói cẩn thận.",
  "Chất liệu tốt, form chuẩn, giá cả hợp lý. Sẽ ủng hộ shop tiếp.",
  "Sản phẩm đúng như mô tả, màu sắc đẹp, size vừa vặn.",
  "Giao hàng nhanh, sản phẩm chất lượng, giá tốt. Recommend!",
  "Thiết kế đẹp, chất liệu ổn, đóng gói cẩn thận. Hài lòng với mua hàng.",
  "Sản phẩm tốt, đúng size, màu sắc như hình. Shop phục vụ nhiệt tình.",
  "Chất lượng ok, giá cả phải chăng, giao hàng đúng hẹn.",
  "Sản phẩm đẹp, form chuẩn, chất liệu tốt. Sẽ mua thêm.",
];

async function generateSampleData() {
  console.log("🚀 Bắt đầu tạo dữ liệu mẫu...");

  // Tạo sample users
  const createdUsers = [];
  for (const userData of sampleUsers) {
    const password = await bcrypt.hash("user123", 12);
    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {},
      create: {
        ...userData,
        password,
        role: "USER",
      },
    });
    createdUsers.push(user);
    console.log(`✅ Tạo user: ${user.name}`);
  }

  // Tạo thêm products
  for (const productData of fashionProducts) {
    const category = await prisma.category.findUnique({
      where: { slug: productData.category },
    });

    if (category) {
      const slug = productData.name
        .toLowerCase()
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "")
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .trim();

      const sku = `${productData.category.toUpperCase()}-${Math.random()
        .toString(36)
        .substr(2, 6)
        .toUpperCase()}`;

      const product = await prisma.product.upsert({
        where: { slug },
        update: {},
        create: {
          name: productData.name,
          slug,
          description: productData.description,
          price: productData.price,
          salePrice: productData.salePrice,
          sku,
          stock: Math.floor(Math.random() * 50) + 10, // Random stock 10-60
          categoryId: category.id,
          images: productData.images,
          featured: Math.random() > 0.7, // 30% chance to be featured
          status: "ACTIVE",
        },
      });

      console.log(`✅ Tạo sản phẩm: ${product.name}`);

      // Tạo reviews cho sản phẩm
      const numReviews = Math.min(
        Math.floor(Math.random() * 5) + 1,
        createdUsers.length
      ); // 1-5 reviews, không vượt quá số users
      const usedUsers = new Set<string>();

      for (let i = 0; i < numReviews; i++) {
        let randomUser;
        let attempts = 0;

        // Tìm user chưa review sản phẩm này
        do {
          randomUser =
            createdUsers[Math.floor(Math.random() * createdUsers.length)];
          attempts++;
        } while (usedUsers.has(randomUser.id) && attempts < 10);

        if (!usedUsers.has(randomUser.id)) {
          usedUsers.add(randomUser.id);

          const randomComment =
            reviewComments[Math.floor(Math.random() * reviewComments.length)];
          const rating = Math.floor(Math.random() * 2) + 4; // 4-5 stars mostly

          try {
            await prisma.review.create({
              data: {
                userId: randomUser.id,
                productId: product.id,
                rating,
                comment: randomComment,
              },
            });
          } catch {
            // Skip if duplicate (shouldn't happen with our logic, but just in case)
            console.log(
              `⚠️  Skipped duplicate review for ${randomUser.name} on ${product.name}`
            );
          }
        }
      }

      // Cập nhật avgRating và reviewCount
      const reviews = await prisma.review.findMany({
        where: { productId: product.id },
      });

      if (reviews.length > 0) {
        const avgRating =
          reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length;

        await prisma.product.update({
          where: { id: product.id },
          data: {
            avgRating: Math.round(avgRating * 10) / 10,
            reviewCount: reviews.length,
          },
        });
      }
    }
  }

  // Tạo sample orders
  for (const user of createdUsers.slice(0, 3)) {
    const products = await prisma.product.findMany({ take: 3 });

    for (let i = 0; i < 2; i++) {
      // 2 orders per user
      const orderProducts = products.slice(
        0,
        Math.floor(Math.random() * 3) + 1
      );
      let total = 0;

      const order = await prisma.order.create({
        data: {
          userId: user.id,
          total: 0, // Will update after creating items
          status: ["PENDING", "CONFIRMED", "DELIVERED"][
            Math.floor(Math.random() * 3)
          ],
          paymentMethod: ["COD", "BANK_TRANSFER"][
            Math.floor(Math.random() * 2)
          ],
          paymentStatus: "PENDING",
          shippingAddress: {
            fullName: user.name,
            phone: user.phone || "**********",
            address: "123 Đường ABC",
            ward: "Phường 1",
            district: "Quận 1",
            province: "TP.HCM",
          },
          billingAddress: {
            fullName: user.name,
            phone: user.phone || "**********",
            address: "123 Đường ABC",
            ward: "Phường 1",
            district: "Quận 1",
            province: "TP.HCM",
          },
        },
      });

      // Tạo order items
      for (const product of orderProducts) {
        const quantity = Math.floor(Math.random() * 3) + 1;
        const price = product.salePrice || product.price;
        total += price * quantity;

        await prisma.orderItem.create({
          data: {
            orderId: order.id,
            productId: product.id,
            quantity,
            price,
            total: price * quantity,
          },
        });
      }

      // Cập nhật total
      await prisma.order.update({
        where: { id: order.id },
        data: { total },
      });

      console.log(
        `✅ Tạo đơn hàng cho ${user.name}: ${total.toLocaleString("vi-VN")}đ`
      );
    }
  }

  console.log("✅ Tạo dữ liệu mẫu hoàn thành!");
}

generateSampleData()
  .catch((e) => {
    console.error("❌ Lỗi tạo dữ liệu mẫu:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
