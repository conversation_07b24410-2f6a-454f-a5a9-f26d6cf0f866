/**
 * Search test helpers
 * <PERSON><PERSON><PERSON> helper functions để hỗ trợ testing cho search functionality
 */

import { mockProducts, mockSearchQueries } from "../fixtures/mock-data";

// Helper để filter products theo search query
export const filterProductsByQuery = (query: string) => {
  if (!query || query.trim() === "") {
    return [];
  }

  const searchTerm = query.toLowerCase().trim();
  
  return mockProducts.filter(product => {
    // Search in name
    if (product.name.toLowerCase().includes(searchTerm)) {
      return true;
    }
    
    // Search in description
    if (product.description.toLowerCase().includes(searchTerm)) {
      return true;
    }
    
    // Search in tags
    if (product.tags.some(tag => tag.toLowerCase().includes(searchTerm))) {
      return true;
    }
    
    return false;
  });
};

// Helper để filter products theo category
export const filterProductsByCategory = (categoryId: string) => {
  return mockProducts.filter(product => product.categoryId === categoryId);
};

// Helper để filter products theo price range
export const filterProductsByPriceRange = (minPrice?: number, maxPrice?: number) => {
  return mockProducts.filter(product => {
    const price = product.salePrice || product.price;
    
    if (minPrice && price < minPrice) {
      return false;
    }
    
    if (maxPrice && price > maxPrice) {
      return false;
    }
    
    return true;
  });
};

// Helper để filter products theo featured status
export const filterProductsByFeatured = (featured: boolean) => {
  return mockProducts.filter(product => product.featured === featured);
};

// Helper để filter products theo stock status
export const filterProductsByStock = (inStock: boolean) => {
  if (inStock) {
    return mockProducts.filter(product => product.stock > 0 && product.status === "ACTIVE");
  }
  return mockProducts;
};

// Helper để combine tất cả filters
export const filterProducts = (filters: {
  search?: string;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  featured?: boolean;
  inStock?: boolean;
}) => {
  let results = mockProducts;

  // Apply search filter
  if (filters.search) {
    results = filterProductsByQuery(filters.search);
  }

  // Apply category filter
  if (filters.category) {
    results = results.filter(product => product.categoryId === filters.category);
  }

  // Apply price range filter
  if (filters.minPrice || filters.maxPrice) {
    results = results.filter(product => {
      const price = product.salePrice || product.price;
      
      if (filters.minPrice && price < filters.minPrice) {
        return false;
      }
      
      if (filters.maxPrice && price > filters.maxPrice) {
        return false;
      }
      
      return true;
    });
  }

  // Apply featured filter
  if (filters.featured !== undefined) {
    results = results.filter(product => product.featured === filters.featured);
  }

  // Apply stock filter
  if (filters.inStock !== undefined && filters.inStock) {
    results = results.filter(product => product.stock > 0 && product.status === "ACTIVE");
  }

  return results;
};

// Helper để tạo search URL với parameters
export const buildSearchUrl = (baseUrl: string, params: Record<string, any>) => {
  const url = new URL(baseUrl);
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      url.searchParams.set(key, value.toString());
    }
  });
  
  return url.toString();
};

// Helper để parse search URL parameters
export const parseSearchParams = (url: string) => {
  const urlObj = new URL(url);
  const params: Record<string, any> = {};
  
  urlObj.searchParams.forEach((value, key) => {
    // Convert numeric values
    if (key === "minPrice" || key === "maxPrice" || key === "page" || key === "limit") {
      params[key] = parseInt(value, 10);
    }
    // Convert boolean values
    else if (key === "featured" || key === "inStock") {
      params[key] = value === "true";
    }
    // Keep string values
    else {
      params[key] = value;
    }
  });
  
  return params;
};

// Helper để validate search query
export const validateSearchQuery = (query: string) => {
  const errors: string[] = [];
  
  if (typeof query !== "string") {
    errors.push("Search query must be a string");
  }
  
  if (query.length > 100) {
    errors.push("Search query must be less than 100 characters");
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Helper để tạo mock search response
export const createMockSearchResponse = (
  products: any[],
  page: number = 1,
  limit: number = 10
) => {
  const total = products.length;
  const pages = Math.ceil(total / limit);
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedProducts = products.slice(startIndex, endIndex);

  return {
    products: paginatedProducts,
    pagination: {
      total,
      page,
      limit,
      pages,
    },
  };
};

// Helper để test search performance
export const measureSearchPerformance = (searchFn: () => any) => {
  const startTime = performance.now();
  const result = searchFn();
  const endTime = performance.now();
  
  return {
    result,
    duration: endTime - startTime,
  };
};

// Export search test queries
export { mockSearchQueries };
