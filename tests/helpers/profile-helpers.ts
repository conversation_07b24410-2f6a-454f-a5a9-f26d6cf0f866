/**
 * Profile test helpers
 * <PERSON><PERSON><PERSON> helper functions để hỗ trợ testing cho profile functionality
 */

import { mockUsers, mockProfileUpdates } from "../fixtures/mock-data";

// Helper để validate profile data
export const validateProfileData = (data: {
  name?: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: string;
}) => {
  const errors: string[] = [];

  // Validate name
  if (!data.name || data.name.trim() === "") {
    errors.push("Họ tên là bắt buộc");
  } else if (data.name.length < 2) {
    errors.push("Họ tên phải có ít nhất 2 ký tự");
  } else if (data.name.length > 100) {
    errors.push("Họ tên không được quá 100 ký tự");
  }

  // Validate phone (optional)
  if (data.phone && data.phone.trim() !== "") {
    const phoneRegex = /^[0-9]{10,11}$/;
    if (!phoneRegex.test(data.phone.replace(/\s/g, ""))) {
      errors.push("Số điện thoại không hợp lệ");
    }
  }

  // Validate date of birth (optional)
  if (data.dateOfBirth && data.dateOfBirth.trim() !== "") {
    const date = new Date(data.dateOfBirth);
    if (isNaN(date.getTime())) {
      errors.push("Ngày sinh không hợp lệ");
    } else {
      const today = new Date();
      const age = today.getFullYear() - date.getFullYear();
      if (age < 13 || age > 120) {
        errors.push("Tuổi phải từ 13 đến 120");
      }
    }
  }

  // Validate gender (optional)
  if (data.gender && !["MALE", "FEMALE", "OTHER"].includes(data.gender)) {
    errors.push("Giới tính không hợp lệ");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Helper để tạo mock user profile
export const createMockUserProfile = (overrides: Partial<typeof mockUsers[0]> = {}) => {
  return {
    ...mockUsers[0],
    ...overrides,
  };
};

// Helper để tạo mock profile update request
export const createMockProfileUpdateRequest = (
  overrides: Partial<{
    name: string;
    phone: string;
    dateOfBirth: string;
    gender: string;
  }> = {}
) => {
  return {
    name: "Test User",
    phone: "0123456789",
    dateOfBirth: "1990-01-01",
    gender: "MALE",
    ...overrides,
  };
};

// Helper để compare profile data
export const compareProfileData = (
  original: any,
  updated: any,
  fieldsToCompare: string[] = ["name", "phone", "dateOfBirth", "gender"]
) => {
  const differences: Record<string, { old: any; new: any }> = {};

  fieldsToCompare.forEach(field => {
    if (original[field] !== updated[field]) {
      differences[field] = {
        old: original[field],
        new: updated[field],
      };
    }
  });

  return {
    hasChanges: Object.keys(differences).length > 0,
    differences,
  };
};

// Helper để format date cho display
export const formatDateForDisplay = (dateString: string | Date | null) => {
  if (!dateString) return "";
  
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return "";
  
  return date.toLocaleDateString("vi-VN", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

// Helper để format date cho input
export const formatDateForInput = (dateString: string | Date | null) => {
  if (!dateString) return "";
  
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return "";
  
  return date.toISOString().split("T")[0];
};

// Helper để format gender cho display
export const formatGenderForDisplay = (gender: string | null) => {
  switch (gender) {
    case "MALE":
      return "Nam";
    case "FEMALE":
      return "Nữ";
    case "OTHER":
      return "Khác";
    default:
      return "Chưa cập nhật";
  }
};

// Helper để tạo mock API response
export const createMockProfileResponse = (
  user: any,
  success: boolean = true,
  errorMessage?: string
) => {
  if (success) {
    const { password, ...userProfile } = user;
    return {
      status: 200,
      data: userProfile,
    };
  } else {
    return {
      status: 400,
      data: {
        error: errorMessage || "Có lỗi xảy ra",
      },
    };
  }
};

// Helper để simulate API delay
export const simulateApiDelay = (ms: number = 500) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Helper để test form validation
export const testFormValidation = (formData: any, expectedErrors: string[]) => {
  const validation = validateProfileData(formData);
  
  return {
    passed: validation.errors.length === expectedErrors.length &&
            expectedErrors.every(error => validation.errors.includes(error)),
    actualErrors: validation.errors,
    expectedErrors,
  };
};

// Helper để generate random profile data
export const generateRandomProfileData = () => {
  const names = ["Nguyễn Văn A", "Trần Thị B", "Lê Văn C", "Phạm Thị D"];
  const phones = ["0123456789", "0987654321", "0555666777", "0999888777"];
  const genders = ["MALE", "FEMALE", "OTHER"];
  
  const randomDate = new Date(
    1980 + Math.floor(Math.random() * 30), // Year between 1980-2010
    Math.floor(Math.random() * 12), // Month 0-11
    1 + Math.floor(Math.random() * 28) // Day 1-28
  );

  return {
    name: names[Math.floor(Math.random() * names.length)],
    phone: phones[Math.floor(Math.random() * phones.length)],
    dateOfBirth: formatDateForInput(randomDate),
    gender: genders[Math.floor(Math.random() * genders.length)],
  };
};

// Helper để test profile update flow
export const testProfileUpdateFlow = async (
  originalProfile: any,
  updateData: any,
  updateFn: (data: any) => Promise<any>
) => {
  const startTime = Date.now();
  
  try {
    // Validate update data
    const validation = validateProfileData(updateData);
    if (!validation.isValid) {
      return {
        success: false,
        errors: validation.errors,
        duration: Date.now() - startTime,
      };
    }

    // Perform update
    const result = await updateFn(updateData);
    
    // Compare results
    const comparison = compareProfileData(originalProfile, result);
    
    return {
      success: true,
      result,
      changes: comparison.differences,
      duration: Date.now() - startTime,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      duration: Date.now() - startTime,
    };
  }
};

// Export profile test data
export { mockProfileUpdates };
