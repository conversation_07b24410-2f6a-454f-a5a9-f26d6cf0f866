/**
 * Authentication utilities unit tests
 * Ki<PERSON><PERSON> tra unit cho authentication functions
 */

import { createMockUser } from "../../helpers/test-utils";

// Mock authentication utilities
const mockAuthUtils = {
  generateToken: jest.fn(),
  verifyToken: jest.fn(),
  hashPassword: jest.fn(),
  comparePassword: jest.fn(),
};

// Mock cookie utilities
const mockCookieUtils = {
  setAuthCookie: jest.fn(),
  getAuthCookie: jest.fn(),
  clearAuthCookie: jest.fn(),
};

describe("Authentication Utilities", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Token Utilities", () => {
    describe("generateToken", () => {
      it("should generate JWT token for user", async () => {
        const user = createMockUser();
        const expectedToken = "mock-jwt-token";

        mockAuthUtils.generateToken.mockResolvedValue(expectedToken);

        const token = await mockAuthUtils.generateToken(user);

        expect(token).toBe(expectedToken);
        expect(mockAuthUtils.generateToken).toHaveBeenCalledWith(user);
      });

      it("should handle token generation failure", async () => {
        const user = createMockUser();

        mockAuthUtils.generateToken.mockRejectedValue(
          new Error("Token generation failed")
        );

        await expect(mockAuthUtils.generateToken(user)).rejects.toThrow(
          "Token generation failed"
        );
      });
    });

    describe("verifyToken", () => {
      it("should verify valid JWT token", async () => {
        const token = "valid-jwt-token";
        const payload = { sub: "user-id", exp: Date.now() / 1000 + 3600 };

        mockAuthUtils.verifyToken.mockResolvedValue(payload);

        const result = await mockAuthUtils.verifyToken(token);

        expect(result).toEqual(payload);
        expect(mockAuthUtils.verifyToken).toHaveBeenCalledWith(token);
      });

      it("should reject invalid JWT token", async () => {
        const token = "invalid-jwt-token";

        mockAuthUtils.verifyToken.mockRejectedValue(new Error("Invalid token"));

        await expect(mockAuthUtils.verifyToken(token)).rejects.toThrow(
          "Invalid token"
        );
      });

      it("should reject expired JWT token", async () => {
        const token = "expired-jwt-token";

        mockAuthUtils.verifyToken.mockRejectedValue(new Error("Token expired"));

        await expect(mockAuthUtils.verifyToken(token)).rejects.toThrow(
          "Token expired"
        );
      });
    });
  });

  describe("Cookie Management", () => {
    describe("setAuthCookie", () => {
      it("should set authentication cookie", () => {
        const token = "jwt-token";
        const options = { httpOnly: true, secure: true };

        mockCookieUtils.setAuthCookie(token, options);

        expect(mockCookieUtils.setAuthCookie).toHaveBeenCalledWith(
          token,
          options
        );
      });
    });

    describe("getAuthCookie", () => {
      it("should get authentication cookie", () => {
        const expectedToken = "jwt-token";
        mockCookieUtils.getAuthCookie.mockReturnValue(expectedToken);

        const token = mockCookieUtils.getAuthCookie();

        expect(token).toBe(expectedToken);
        expect(mockCookieUtils.getAuthCookie).toHaveBeenCalled();
      });

      it("should return null when no cookie exists", () => {
        mockCookieUtils.getAuthCookie.mockReturnValue(null);

        const token = mockCookieUtils.getAuthCookie();

        expect(token).toBeNull();
      });
    });

    describe("clearAuthCookie", () => {
      it("should clear authentication cookie", () => {
        mockCookieUtils.clearAuthCookie();

        expect(mockCookieUtils.clearAuthCookie).toHaveBeenCalled();
      });
    });
  });

  describe("Password Utilities", () => {
    describe("hashPassword", () => {
      it("should hash password securely", async () => {
        const password = "password123";
        const hashedPassword = "hashed-password";

        mockAuthUtils.hashPassword.mockResolvedValue(hashedPassword);

        const result = await mockAuthUtils.hashPassword(password);

        expect(result).toBe(hashedPassword);
        expect(mockAuthUtils.hashPassword).toHaveBeenCalledWith(password);
      });
    });

    describe("comparePassword", () => {
      it("should compare password with hash correctly", async () => {
        const password = "password123";
        const hash = "hashed-password";

        mockAuthUtils.comparePassword.mockResolvedValue(true);

        const result = await mockAuthUtils.comparePassword(password, hash);

        expect(result).toBe(true);
        expect(mockAuthUtils.comparePassword).toHaveBeenCalledWith(
          password,
          hash
        );
      });

      it("should return false for incorrect password", async () => {
        const password = "wrongpassword";
        const hash = "hashed-password";

        mockAuthUtils.comparePassword.mockResolvedValue(false);

        const result = await mockAuthUtils.comparePassword(password, hash);

        expect(result).toBe(false);
      });
    });
  });

  describe("Authentication Service", () => {
    const mockAuthService = {
      usernamePasswordLogin: jest.fn(),
    };

    describe("usernamePasswordLogin", () => {
      it("should login with valid credentials", async () => {
        const username = "testuser";
        const password = "password123";
        const user = createMockUser({ username });

        mockAuthService.usernamePasswordLogin.mockResolvedValue(user);

        const result = await mockAuthService.usernamePasswordLogin(
          username,
          password
        );

        expect(result).toEqual(user);
        expect(mockAuthService.usernamePasswordLogin).toHaveBeenCalledWith(
          username,
          password
        );
      });

      it("should reject invalid credentials", async () => {
        const username = "testuser";
        const password = "wrongpassword";

        mockAuthService.usernamePasswordLogin.mockRejectedValue(
          new Error("Invalid password")
        );

        await expect(
          mockAuthService.usernamePasswordLogin(username, password)
        ).rejects.toThrow("Invalid password");
      });

      it("should create new user if not exists", async () => {
        const username = "newuser";
        const password = "password123";
        const newUser = createMockUser({ username });

        mockAuthService.usernamePasswordLogin.mockResolvedValue(newUser);

        const result = await mockAuthService.usernamePasswordLogin(
          username,
          password
        );

        expect(result).toEqual(newUser);
        expect(result.username).toBe(username);
      });
    });

    describe("logout", () => {
      it("should clear auth cookie", () => {
        mockCookieUtils.clearAuthCookie();

        expect(mockCookieUtils.clearAuthCookie).toHaveBeenCalled();
      });
    });
  });

  describe("Authentication Validation", () => {
    describe("validateCredentials", () => {
      it("should validate email format", () => {
        const validEmails = [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ];

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        validEmails.forEach((email) => {
          expect(emailRegex.test(email)).toBe(true);
        });
      });

      it("should reject invalid email formats", () => {
        const invalidEmails = [
          "invalid-email",
          "@example.com",
          "user@",
          "user@.com",
          // "<EMAIL>", // This actually passes the simple regex
        ];

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        invalidEmails.forEach((email) => {
          expect(emailRegex.test(email)).toBe(false);
        });
      });

      it("should validate password strength", () => {
        const strongPasswords = [
          "Password123!",
          "MyStr0ngP@ssw0rd",
          "C0mpl3x!P@ssw0rd",
        ];

        // Password should have at least 8 characters, 1 uppercase, 1 lowercase, 1 number
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;

        strongPasswords.forEach((password) => {
          expect(passwordRegex.test(password)).toBe(true);
        });
      });

      it("should reject weak passwords", () => {
        const weakPasswords = [
          "password",
          "123456",
          "PASSWORD",
          "Pass123", // Too short
          "password123", // No uppercase
        ];

        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;

        weakPasswords.forEach((password) => {
          expect(passwordRegex.test(password)).toBe(false);
        });
      });
    });
  });
});
