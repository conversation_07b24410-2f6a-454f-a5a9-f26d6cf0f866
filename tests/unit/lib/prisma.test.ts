/**
 * Prisma Client Tests
 * Ki<PERSON>m tra unit cho Prisma client và database utilities
 */

import { PrismaClient } from "@prisma/client";

// Mock PrismaClient
jest.mock("@prisma/client", () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    $connect: jest.fn(),
    $disconnect: jest.fn(),
    user: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    product: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    category: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    order: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    cartItem: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
  })),
}));

// Mock the prisma instance
const mockPrisma = {
  $connect: jest.fn(),
  $disconnect: jest.fn(),
  user: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  product: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
  },
  category: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  order: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  cartItem: {
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
};

describe("Prisma Client", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Connection Management", () => {
    it("should create PrismaClient instance", () => {
      const prisma = new PrismaClient();
      expect(PrismaClient).toHaveBeenCalled();
      expect(prisma).toBeDefined();
    });

    it("should connect to database", async () => {
      await mockPrisma.$connect();
      expect(mockPrisma.$connect).toHaveBeenCalled();
    });

    it("should disconnect from database", async () => {
      await mockPrisma.$disconnect();
      expect(mockPrisma.$disconnect).toHaveBeenCalled();
    });
  });

  describe("User Operations", () => {
    const mockUser = {
      id: "user_123",
      name: "John Doe",
      email: "<EMAIL>",
      password: "hashed_password",
      role: "USER",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it("should find user by email", async () => {
      mockPrisma.user.findUnique.mockResolvedValue(mockUser);

      const result = await mockPrisma.user.findUnique({
        where: { email: "<EMAIL>" },
      });

      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { email: "<EMAIL>" },
      });
      expect(result).toEqual(mockUser);
    });

    it("should create new user", async () => {
      const userData = {
        name: "John Doe",
        email: "<EMAIL>",
        password: "hashed_password",
      };

      mockPrisma.user.create.mockResolvedValue(mockUser);

      const result = await mockPrisma.user.create({
        data: userData,
      });

      expect(mockPrisma.user.create).toHaveBeenCalledWith({
        data: userData,
      });
      expect(result).toEqual(mockUser);
    });

    it("should find multiple users", async () => {
      const mockUsers = [mockUser];
      mockPrisma.user.findMany.mockResolvedValue(mockUsers);

      const result = await mockPrisma.user.findMany({
        where: { role: "USER" },
      });

      expect(mockPrisma.user.findMany).toHaveBeenCalledWith({
        where: { role: "USER" },
      });
      expect(result).toEqual(mockUsers);
    });

    it("should update user", async () => {
      const updatedUser = { ...mockUser, name: "Jane Doe" };
      mockPrisma.user.update.mockResolvedValue(updatedUser);

      const result = await mockPrisma.user.update({
        where: { id: "user_123" },
        data: { name: "Jane Doe" },
      });

      expect(mockPrisma.user.update).toHaveBeenCalledWith({
        where: { id: "user_123" },
        data: { name: "Jane Doe" },
      });
      expect(result).toEqual(updatedUser);
    });

    it("should delete user", async () => {
      mockPrisma.user.delete.mockResolvedValue(mockUser);

      const result = await mockPrisma.user.delete({
        where: { id: "user_123" },
      });

      expect(mockPrisma.user.delete).toHaveBeenCalledWith({
        where: { id: "user_123" },
      });
      expect(result).toEqual(mockUser);
    });
  });

  describe("Product Operations", () => {
    const mockProduct = {
      id: "product_123",
      name: "Test Product",
      slug: "test-product",
      description: "A test product",
      price: 100000,
      salePrice: null,
      stock: 50,
      images: ["image1.jpg"],
      categoryId: "cat_123",
      featured: false,
      status: "ACTIVE",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it("should find product by slug", async () => {
      mockPrisma.product.findUnique.mockResolvedValue(mockProduct);

      const result = await mockPrisma.product.findUnique({
        where: { slug: "test-product" },
        include: { category: true },
      });

      expect(mockPrisma.product.findUnique).toHaveBeenCalledWith({
        where: { slug: "test-product" },
        include: { category: true },
      });
      expect(result).toEqual(mockProduct);
    });

    it("should find products with pagination", async () => {
      const mockProducts = [mockProduct];
      mockPrisma.product.findMany.mockResolvedValue(mockProducts);

      const result = await mockPrisma.product.findMany({
        skip: 0,
        take: 10,
        where: { status: "ACTIVE" },
        include: { category: true },
      });

      expect(mockPrisma.product.findMany).toHaveBeenCalledWith({
        skip: 0,
        take: 10,
        where: { status: "ACTIVE" },
        include: { category: true },
      });
      expect(result).toEqual(mockProducts);
    });

    it("should count products", async () => {
      mockPrisma.product.count.mockResolvedValue(100);

      const result = await mockPrisma.product.count({
        where: { status: "ACTIVE" },
      });

      expect(mockPrisma.product.count).toHaveBeenCalledWith({
        where: { status: "ACTIVE" },
      });
      expect(result).toBe(100);
    });

    it("should create product", async () => {
      const productData = {
        name: "New Product",
        slug: "new-product",
        description: "A new product",
        price: 200000,
        stock: 30,
        categoryId: "cat_123",
      };

      mockPrisma.product.create.mockResolvedValue({
        ...mockProduct,
        ...productData,
      });

      const result = await mockPrisma.product.create({
        data: productData,
      });

      expect(mockPrisma.product.create).toHaveBeenCalledWith({
        data: productData,
      });
      expect(result.name).toBe(productData.name);
    });
  });

  describe("Category Operations", () => {
    const mockCategory = {
      id: "cat_123",
      name: "Test Category",
      slug: "test-category",
      description: "A test category",
      image: "category.jpg",
      status: "ACTIVE",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it("should find all categories", async () => {
      const mockCategories = [mockCategory];
      mockPrisma.category.findMany.mockResolvedValue(mockCategories);

      const result = await mockPrisma.category.findMany({
        where: { status: "ACTIVE" },
      });

      expect(mockPrisma.category.findMany).toHaveBeenCalledWith({
        where: { status: "ACTIVE" },
      });
      expect(result).toEqual(mockCategories);
    });

    it("should find category by slug", async () => {
      mockPrisma.category.findUnique.mockResolvedValue(mockCategory);

      const result = await mockPrisma.category.findUnique({
        where: { slug: "test-category" },
      });

      expect(mockPrisma.category.findUnique).toHaveBeenCalledWith({
        where: { slug: "test-category" },
      });
      expect(result).toEqual(mockCategory);
    });
  });

  describe("Order Operations", () => {
    const mockOrder = {
      id: "order_123",
      userId: "user_123",
      status: "PENDING",
      total: 100000,
      shippingAddress: {
        fullName: "John Doe",
        phone: "0123456789",
        address: "123 Main St",
        city: "Ho Chi Minh City",
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it("should create order", async () => {
      const orderData = {
        userId: "user_123",
        total: 100000,
        shippingAddress: {
          fullName: "John Doe",
          phone: "0123456789",
          address: "123 Main St",
          city: "Ho Chi Minh City",
        },
      };

      mockPrisma.order.create.mockResolvedValue(mockOrder);

      const result = await mockPrisma.order.create({
        data: orderData,
      });

      expect(mockPrisma.order.create).toHaveBeenCalledWith({
        data: orderData,
      });
      expect(result).toEqual(mockOrder);
    });

    it("should find user orders", async () => {
      const mockOrders = [mockOrder];
      mockPrisma.order.findMany.mockResolvedValue(mockOrders);

      const result = await mockPrisma.order.findMany({
        where: { userId: "user_123" },
        include: { items: true },
      });

      expect(mockPrisma.order.findMany).toHaveBeenCalledWith({
        where: { userId: "user_123" },
        include: { items: true },
      });
      expect(result).toEqual(mockOrders);
    });
  });

  describe("Error Handling", () => {
    it("should handle database connection errors", async () => {
      const error = new Error("Database connection failed");
      mockPrisma.$connect.mockRejectedValue(error);

      await expect(mockPrisma.$connect()).rejects.toThrow("Database connection failed");
    });

    it("should handle query errors", async () => {
      const error = new Error("Query failed");
      mockPrisma.user.findUnique.mockRejectedValue(error);

      await expect(
        mockPrisma.user.findUnique({ where: { id: "invalid" } })
      ).rejects.toThrow("Query failed");
    });

    it("should handle unique constraint violations", async () => {
      const error = new Error("Unique constraint failed");
      mockPrisma.user.create.mockRejectedValue(error);

      await expect(
        mockPrisma.user.create({
          data: {
            name: "Test",
            email: "<EMAIL>",
            password: "password",
          },
        })
      ).rejects.toThrow("Unique constraint failed");
    });
  });
});
