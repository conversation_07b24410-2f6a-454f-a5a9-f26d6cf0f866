/**
 * Utils library unit tests
 * <PERSON><PERSON>m tra unit cho các utility functions
 */

import {
  cn,
  formatCurrency,
  formatNumber,
  truncateText,
  generateSlug,
  getSystemTheme,
  setTheme,
  getTheme,
} from "@/lib/utils";

// Mock window và localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, "localStorage", {
  value: mockLocalStorage,
  writable: true,
});

// Mock window.matchMedia
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

describe("Utils Library", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset document classes
    document.documentElement.className = "";
  });

  describe("cn (className utility)", () => {
    it("should merge class names correctly", () => {
      const result = cn("px-4", "py-2", "bg-blue-500");
      expect(result).toBe("px-4 py-2 bg-blue-500");
    });

    it("should handle conditional classes", () => {
      const isActive = true;
      const result = cn("base-class", isActive && "active-class");
      expect(result).toBe("base-class active-class");
    });

    it("should handle falsy values", () => {
      const result = cn("base-class", false, null, undefined, "valid-class");
      expect(result).toBe("base-class valid-class");
    });

    it("should merge conflicting Tailwind classes", () => {
      const result = cn("px-4 px-6", "py-2 py-4");
      // twMerge should resolve conflicts, keeping the last one
      expect(result).toContain("px-6");
      expect(result).toContain("py-4");
    });

    it("should handle empty input", () => {
      const result = cn();
      expect(result).toBe("");
    });
  });

  describe("formatCurrency", () => {
    it("should format VND currency correctly", () => {
      const result = formatCurrency(100000);
      expect(result).toContain("100.000");
      expect(result).toContain("₫");
    });

    it("should format USD currency correctly", () => {
      const result = formatCurrency(100, "USD");
      expect(result).toContain("100");
      expect(result).toContain("US$");
    });

    it("should handle zero amount", () => {
      const result = formatCurrency(0);
      expect(result).toContain("0");
      expect(result).toContain("₫");
    });

    it("should handle negative amounts", () => {
      const result = formatCurrency(-50000);
      expect(result).toContain("-50.000");
      expect(result).toContain("₫");
    });

    it("should handle decimal amounts", () => {
      const result = formatCurrency(99.99);
      expect(result).toContain("100"); // VND rounds to nearest integer
      expect(result).toContain("₫");
    });
  });

  describe("formatNumber", () => {
    it("should format numbers with Vietnamese locale", () => {
      const result = formatNumber(1234567);
      expect(result).toBe("1.234.567");
    });

    it("should handle zero", () => {
      const result = formatNumber(0);
      expect(result).toBe("0");
    });

    it("should handle negative numbers", () => {
      const result = formatNumber(-1234);
      expect(result).toBe("-1.234");
    });

    it("should handle decimal numbers", () => {
      const result = formatNumber(123.45);
      expect(result).toMatch(/123/);
    });
  });

  describe("truncateText", () => {
    it("should truncate text longer than maxLength", () => {
      const text = "This is a very long text that should be truncated";
      const result = truncateText(text, 20);
      expect(result).toBe("This is a very long ...");
      expect(result.length).toBe(23); // 20 + '...'
    });

    it("should not truncate text shorter than maxLength", () => {
      const text = "Short text";
      const result = truncateText(text, 20);
      expect(result).toBe("Short text");
    });

    it("should handle text exactly at maxLength", () => {
      const text = "Exactly twenty chars";
      const result = truncateText(text, 20);
      expect(result).toBe("Exactly twenty chars");
    });

    it("should handle empty string", () => {
      const result = truncateText("", 10);
      expect(result).toBe("");
    });

    it("should handle maxLength of 0", () => {
      const result = truncateText("Some text", 0);
      expect(result).toBe("...");
    });
  });

  describe("generateSlug", () => {
    it("should generate slug from Vietnamese text", () => {
      const result = generateSlug("Áo thun nam đẹp");
      expect(result).toBe("ao-thun-nam-dep");
    });

    it("should handle special characters", () => {
      const result = generateSlug("Product #1 - Special @#$%");
      expect(result).toBe("product-1-special-");
    });

    it("should handle multiple spaces", () => {
      const result = generateSlug("Multiple    spaces   here");
      expect(result).toBe("multiple-spaces-here");
    });

    it("should handle đ character", () => {
      const result = generateSlug("Đồng hồ đẹp");
      expect(result).toBe("dong-ho-dep");
    });

    it("should handle uppercase text", () => {
      const result = generateSlug("UPPERCASE TEXT");
      expect(result).toBe("uppercase-text");
    });

    it("should handle empty string", () => {
      const result = generateSlug("");
      expect(result).toBe("");
    });

    it("should handle text with only special characters", () => {
      const result = generateSlug("@#$%^&*()");
      expect(result).toBe("");
    });

    it("should remove leading and trailing hyphens", () => {
      const result = generateSlug("  -text-  ");
      expect(result).toBe("-text-");
    });
  });

  describe("Theme utilities", () => {
    describe("getSystemTheme", () => {
      it("should return dark when system prefers dark", () => {
        window.matchMedia = jest.fn().mockImplementation(() => ({
          matches: true,
        }));

        const result = getSystemTheme();
        expect(result).toBe("dark");
      });

      it("should return light when system prefers light", () => {
        window.matchMedia = jest.fn().mockImplementation(() => ({
          matches: false,
        }));

        const result = getSystemTheme();
        expect(result).toBe("light");
      });

      it("should return light on server side", () => {
        // Mock SSR environment
        const originalWindow = global.window;
        delete (global as any).window;

        const result = getSystemTheme();
        expect(result).toBe("light");

        // Restore window
        global.window = originalWindow;
      });
    });

    describe("setTheme", () => {
      it("should set light theme", () => {
        setTheme("light");

        expect(mockLocalStorage.setItem).toHaveBeenCalledWith("theme", "light");
        expect(document.documentElement.classList.contains("dark")).toBe(false);
      });

      it("should set dark theme", () => {
        setTheme("dark");

        expect(mockLocalStorage.setItem).toHaveBeenCalledWith("theme", "dark");
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      it("should set system theme based on system preference", () => {
        window.matchMedia = jest.fn().mockImplementation(() => ({
          matches: true, // System prefers dark
        }));

        setTheme("system");

        expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
          "theme",
          "system"
        );
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      it("should not run on server side", () => {
        const originalWindow = global.window;
        delete (global as any).window;

        setTheme("dark");

        // The function should still be called but won't affect anything
        // since window is undefined
        expect(true).toBe(true); // Just verify the function doesn't crash

        global.window = originalWindow;
      });
    });

    describe("getTheme", () => {
      it("should return stored theme from localStorage", () => {
        mockLocalStorage.getItem.mockReturnValue("dark");

        const result = getTheme();
        expect(result).toBe("dark");
        expect(mockLocalStorage.getItem).toHaveBeenCalledWith("theme");
      });

      it("should return system as default when no theme stored", () => {
        mockLocalStorage.getItem.mockReturnValue(null);

        const result = getTheme();
        expect(result).toBe("system");
      });

      it("should return system on server side", () => {
        const originalWindow = global.window;
        delete (global as any).window;

        const result = getTheme();
        expect(result).toBe("system");

        global.window = originalWindow;
      });
    });
  });
});
