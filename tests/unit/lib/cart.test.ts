/**
 * Cart management utilities unit tests
 * Kiểm tra unit cho cart management functions
 */

import { createMockProduct, createMockCartItem, createMockUser } from '../../helpers/test-utils'

// Mock cart types based on the types we saw
interface CartItem {
  id: string
  productId: string
  product: any
  quantity: number
  price: number
}

interface Cart {
  id: string
  userId?: string
  items: CartItem[]
  total: number
  createdAt: Date
  updatedAt: Date
}

describe('Cart Management Utilities', () => {
  describe('Cart Calculations', () => {
    describe('calculateItemTotal', () => {
      it('should calculate total for single item', () => {
        const calculateItemTotal = (price: number, quantity: number) => price * quantity
        
        const result = calculateItemTotal(100, 2)
        expect(result).toBe(200)
      })

      it('should handle zero quantity', () => {
        const calculateItemTotal = (price: number, quantity: number) => price * quantity
        
        const result = calculateItemTotal(100, 0)
        expect(result).toBe(0)
      })

      it('should handle decimal prices', () => {
        const calculateItemTotal = (price: number, quantity: number) => price * quantity
        
        const result = calculateItemTotal(99.99, 3)
        expect(result).toBeCloseTo(299.97, 2)
      })
    })

    describe('calculateCartTotal', () => {
      it('should calculate total for multiple items', () => {
        const items = [
          { price: 100, quantity: 2 }, // 200
          { price: 50, quantity: 1 },  // 50
          { price: 75, quantity: 3 },  // 225
        ]
        
        const calculateCartTotal = (items: { price: number; quantity: number }[]) => {
          return items.reduce((total, item) => total + (item.price * item.quantity), 0)
        }
        
        const result = calculateCartTotal(items)
        expect(result).toBe(475)
      })

      it('should handle empty cart', () => {
        const calculateCartTotal = (items: { price: number; quantity: number }[]) => {
          return items.reduce((total, item) => total + (item.price * item.quantity), 0)
        }
        
        const result = calculateCartTotal([])
        expect(result).toBe(0)
      })

      it('should handle items with zero quantity', () => {
        const items = [
          { price: 100, quantity: 2 },
          { price: 50, quantity: 0 },
          { price: 75, quantity: 1 },
        ]
        
        const calculateCartTotal = (items: { price: number; quantity: number }[]) => {
          return items.reduce((total, item) => total + (item.price * item.quantity), 0)
        }
        
        const result = calculateCartTotal(items)
        expect(result).toBe(275) // 200 + 0 + 75
      })
    })

    describe('calculateDiscount', () => {
      it('should calculate percentage discount', () => {
        const calculateDiscount = (total: number, discountPercent: number) => {
          return total * (discountPercent / 100)
        }
        
        const result = calculateDiscount(1000, 10)
        expect(result).toBe(100)
      })

      it('should calculate fixed amount discount', () => {
        const calculateFixedDiscount = (total: number, discountAmount: number) => {
          return Math.min(discountAmount, total)
        }
        
        const result = calculateFixedDiscount(1000, 150)
        expect(result).toBe(150)
      })

      it('should not exceed total amount for fixed discount', () => {
        const calculateFixedDiscount = (total: number, discountAmount: number) => {
          return Math.min(discountAmount, total)
        }
        
        const result = calculateFixedDiscount(100, 150)
        expect(result).toBe(100)
      })
    })
  })

  describe('Cart Item Management', () => {
    describe('addItemToCart', () => {
      it('should add new item to empty cart', () => {
        const cart: CartItem[] = []
        const product = createMockProduct()
        
        const addItemToCart = (cart: CartItem[], productId: string, quantity: number, price: number) => {
          const existingItem = cart.find(item => item.productId === productId)
          
          if (existingItem) {
            return cart.map(item =>
              item.productId === productId
                ? { ...item, quantity: item.quantity + quantity }
                : item
            )
          }
          
          return [...cart, {
            id: `item-${Date.now()}`,
            productId,
            product: null,
            quantity,
            price,
          }]
        }
        
        const result = addItemToCart(cart, product.id, 2, product.price)
        
        expect(result).toHaveLength(1)
        expect(result[0].productId).toBe(product.id)
        expect(result[0].quantity).toBe(2)
        expect(result[0].price).toBe(product.price)
      })

      it('should increase quantity for existing item', () => {
        const existingItem = {
          id: 'item-1',
          productId: 'prod-1',
          product: null,
          quantity: 1,
          price: 100,
        }
        const cart = [existingItem]
        
        const addItemToCart = (cart: CartItem[], productId: string, quantity: number, price: number) => {
          const existingItem = cart.find(item => item.productId === productId)
          
          if (existingItem) {
            return cart.map(item =>
              item.productId === productId
                ? { ...item, quantity: item.quantity + quantity }
                : item
            )
          }
          
          return [...cart, {
            id: `item-${Date.now()}`,
            productId,
            product: null,
            quantity,
            price,
          }]
        }
        
        const result = addItemToCart(cart, 'prod-1', 2, 100)
        
        expect(result).toHaveLength(1)
        expect(result[0].quantity).toBe(3) // 1 + 2
      })

      it('should add different products separately', () => {
        const cart: CartItem[] = [{
          id: 'item-1',
          productId: 'prod-1',
          product: null,
          quantity: 1,
          price: 100,
        }]
        
        const addItemToCart = (cart: CartItem[], productId: string, quantity: number, price: number) => {
          const existingItem = cart.find(item => item.productId === productId)
          
          if (existingItem) {
            return cart.map(item =>
              item.productId === productId
                ? { ...item, quantity: item.quantity + quantity }
                : item
            )
          }
          
          return [...cart, {
            id: `item-${Date.now()}`,
            productId,
            product: null,
            quantity,
            price,
          }]
        }
        
        const result = addItemToCart(cart, 'prod-2', 1, 200)
        
        expect(result).toHaveLength(2)
        expect(result[1].productId).toBe('prod-2')
        expect(result[1].quantity).toBe(1)
      })
    })

    describe('removeItemFromCart', () => {
      it('should remove item completely', () => {
        const cart = [
          { id: 'item-1', productId: 'prod-1', product: null, quantity: 2, price: 100 },
          { id: 'item-2', productId: 'prod-2', product: null, quantity: 1, price: 200 },
        ]
        
        const removeItemFromCart = (cart: CartItem[], productId: string) => {
          return cart.filter(item => item.productId !== productId)
        }
        
        const result = removeItemFromCart(cart, 'prod-1')
        
        expect(result).toHaveLength(1)
        expect(result[0].productId).toBe('prod-2')
      })

      it('should handle removing non-existent item', () => {
        const cart = [
          { id: 'item-1', productId: 'prod-1', product: null, quantity: 2, price: 100 },
        ]
        
        const removeItemFromCart = (cart: CartItem[], productId: string) => {
          return cart.filter(item => item.productId !== productId)
        }
        
        const result = removeItemFromCart(cart, 'non-existent')
        
        expect(result).toHaveLength(1)
        expect(result[0].productId).toBe('prod-1')
      })
    })

    describe('updateItemQuantity', () => {
      it('should update item quantity', () => {
        const cart = [
          { id: 'item-1', productId: 'prod-1', product: null, quantity: 2, price: 100 },
        ]
        
        const updateItemQuantity = (cart: CartItem[], productId: string, quantity: number) => {
          return cart.map(item =>
            item.productId === productId
              ? { ...item, quantity }
              : item
          )
        }
        
        const result = updateItemQuantity(cart, 'prod-1', 5)
        
        expect(result[0].quantity).toBe(5)
      })

      it('should remove item when quantity is 0', () => {
        const cart = [
          { id: 'item-1', productId: 'prod-1', product: null, quantity: 2, price: 100 },
        ]
        
        const updateItemQuantity = (cart: CartItem[], productId: string, quantity: number) => {
          if (quantity <= 0) {
            return cart.filter(item => item.productId !== productId)
          }
          
          return cart.map(item =>
            item.productId === productId
              ? { ...item, quantity }
              : item
          )
        }
        
        const result = updateItemQuantity(cart, 'prod-1', 0)
        
        expect(result).toHaveLength(0)
      })

      it('should handle negative quantities', () => {
        const cart = [
          { id: 'item-1', productId: 'prod-1', product: null, quantity: 2, price: 100 },
        ]
        
        const updateItemQuantity = (cart: CartItem[], productId: string, quantity: number) => {
          if (quantity <= 0) {
            return cart.filter(item => item.productId !== productId)
          }
          
          return cart.map(item =>
            item.productId === productId
              ? { ...item, quantity }
              : item
          )
        }
        
        const result = updateItemQuantity(cart, 'prod-1', -1)
        
        expect(result).toHaveLength(0)
      })
    })
  })

  describe('Cart Validation', () => {
    describe('validateCartItem', () => {
      it('should validate valid cart item', () => {
        const validateCartItem = (productId: string, quantity: number, price: number) => {
          if (!productId || typeof productId !== 'string') return false
          if (!Number.isInteger(quantity) || quantity <= 0) return false
          if (!Number.isFinite(price) || price <= 0) return false
          return true
        }
        
        const result = validateCartItem('prod-1', 2, 100)
        expect(result).toBe(true)
      })

      it('should reject invalid product ID', () => {
        const validateCartItem = (productId: string, quantity: number, price: number) => {
          if (!productId || typeof productId !== 'string') return false
          if (!Number.isInteger(quantity) || quantity <= 0) return false
          if (!Number.isFinite(price) || price <= 0) return false
          return true
        }
        
        expect(validateCartItem('', 2, 100)).toBe(false)
        expect(validateCartItem(null as any, 2, 100)).toBe(false)
      })

      it('should reject invalid quantity', () => {
        const validateCartItem = (productId: string, quantity: number, price: number) => {
          if (!productId || typeof productId !== 'string') return false
          if (!Number.isInteger(quantity) || quantity <= 0) return false
          if (!Number.isFinite(price) || price <= 0) return false
          return true
        }
        
        expect(validateCartItem('prod-1', 0, 100)).toBe(false)
        expect(validateCartItem('prod-1', -1, 100)).toBe(false)
        expect(validateCartItem('prod-1', 1.5, 100)).toBe(false)
      })

      it('should reject invalid price', () => {
        const validateCartItem = (productId: string, quantity: number, price: number) => {
          if (!productId || typeof productId !== 'string') return false
          if (!Number.isInteger(quantity) || quantity <= 0) return false
          if (!Number.isFinite(price) || price <= 0) return false
          return true
        }
        
        expect(validateCartItem('prod-1', 2, 0)).toBe(false)
        expect(validateCartItem('prod-1', 2, -100)).toBe(false)
        expect(validateCartItem('prod-1', 2, NaN)).toBe(false)
      })
    })

    describe('validateCartTotal', () => {
      it('should validate cart total matches item totals', () => {
        const items = [
          { price: 100, quantity: 2 },
          { price: 50, quantity: 1 },
        ]
        const expectedTotal = 250
        
        const validateCartTotal = (items: { price: number; quantity: number }[], total: number) => {
          const calculatedTotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0)
          return Math.abs(calculatedTotal - total) < 0.01 // Allow for floating point precision
        }
        
        const result = validateCartTotal(items, expectedTotal)
        expect(result).toBe(true)
      })

      it('should reject incorrect total', () => {
        const items = [
          { price: 100, quantity: 2 },
          { price: 50, quantity: 1 },
        ]
        const incorrectTotal = 300
        
        const validateCartTotal = (items: { price: number; quantity: number }[], total: number) => {
          const calculatedTotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0)
          return Math.abs(calculatedTotal - total) < 0.01
        }
        
        const result = validateCartTotal(items, incorrectTotal)
        expect(result).toBe(false)
      })
    })
  })
})
