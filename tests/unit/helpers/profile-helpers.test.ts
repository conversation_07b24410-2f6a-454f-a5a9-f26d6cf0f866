/**
 * Profile Helpers Unit Tests
 * Kiểm tra unit cho profile helper functions
 */

import {
  validateProfileData,
  createMockUserProfile,
  createMockProfileUpdateRequest,
  compareProfileData,
  formatDateForDisplay,
  formatDateForInput,
  formatGenderForDisplay,
  createMockProfileResponse,
  simulateApiDelay,
  testFormValidation,
  generateRandomProfileData,
  testProfileUpdateFlow,
} from "../../helpers/profile-helpers";
import { mockUsers, mockProfileUpdates } from "../../fixtures/mock-data";

describe("Profile Helpers", () => {
  describe("validateProfileData", () => {
    it("should validate valid profile data", () => {
      const validData = mockProfileUpdates.valid[0];
      const result = validateProfileData(validData);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it("should reject empty name", () => {
      const invalidData = { name: "" };
      const result = validateProfileData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Họ tên là bắt buộc");
    });

    it("should reject whitespace-only name", () => {
      const invalidData = { name: "   " };
      const result = validateProfileData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Họ tên là bắt buộc");
    });

    it("should reject name that is too short", () => {
      const invalidData = { name: "A" };
      const result = validateProfileData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Họ tên phải có ít nhất 2 ký tự");
    });

    it("should reject name that is too long", () => {
      const invalidData = { name: "A".repeat(101) };
      const result = validateProfileData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Họ tên không được quá 100 ký tự");
    });

    it("should validate phone number format", () => {
      const invalidData = { name: "Valid Name", phone: "invalid" };
      const result = validateProfileData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Số điện thoại không hợp lệ");
    });

    it("should accept valid phone numbers", () => {
      const validData = { name: "Valid Name", phone: "0123456789" };
      const result = validateProfileData(validData);
      
      expect(result.isValid).toBe(true);
    });

    it("should accept empty phone number", () => {
      const validData = { name: "Valid Name", phone: "" };
      const result = validateProfileData(validData);
      
      expect(result.isValid).toBe(true);
    });

    it("should validate date of birth", () => {
      const invalidData = { name: "Valid Name", dateOfBirth: "invalid-date" };
      const result = validateProfileData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Ngày sinh không hợp lệ");
    });

    it("should reject age under 13", () => {
      const futureDate = new Date();
      futureDate.setFullYear(futureDate.getFullYear() - 10);
      const invalidData = { 
        name: "Valid Name", 
        dateOfBirth: futureDate.toISOString().split('T')[0] 
      };
      const result = validateProfileData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Tuổi phải từ 13 đến 120");
    });

    it("should reject age over 120", () => {
      const oldDate = new Date();
      oldDate.setFullYear(oldDate.getFullYear() - 130);
      const invalidData = { 
        name: "Valid Name", 
        dateOfBirth: oldDate.toISOString().split('T')[0] 
      };
      const result = validateProfileData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Tuổi phải từ 13 đến 120");
    });

    it("should validate gender enum", () => {
      const invalidData = { name: "Valid Name", gender: "INVALID" };
      const result = validateProfileData(invalidData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Giới tính không hợp lệ");
    });

    it("should accept valid gender values", () => {
      const genders = ["MALE", "FEMALE", "OTHER"];
      
      genders.forEach(gender => {
        const validData = { name: "Valid Name", gender };
        const result = validateProfileData(validData);
        expect(result.isValid).toBe(true);
      });
    });

    it("should accept empty optional fields", () => {
      const validData = { 
        name: "Valid Name",
        phone: "",
        dateOfBirth: "",
        gender: "",
      };
      const result = validateProfileData(validData);
      
      expect(result.isValid).toBe(true);
    });
  });

  describe("createMockUserProfile", () => {
    it("should create profile with default values", () => {
      const profile = createMockUserProfile();
      
      expect(profile).toEqual(mockUsers[0]);
    });

    it("should override default values", () => {
      const overrides = { name: "Custom Name", email: "<EMAIL>" };
      const profile = createMockUserProfile(overrides);
      
      expect(profile.name).toBe("Custom Name");
      expect(profile.email).toBe("<EMAIL>");
      expect(profile.id).toBe(mockUsers[0].id); // Should keep other defaults
    });
  });

  describe("createMockProfileUpdateRequest", () => {
    it("should create update request with default values", () => {
      const request = createMockProfileUpdateRequest();
      
      expect(request.name).toBe("Test User");
      expect(request.phone).toBe("0123456789");
      expect(request.dateOfBirth).toBe("1990-01-01");
      expect(request.gender).toBe("MALE");
    });

    it("should override default values", () => {
      const overrides = { name: "Custom Name", gender: "FEMALE" };
      const request = createMockProfileUpdateRequest(overrides);
      
      expect(request.name).toBe("Custom Name");
      expect(request.gender).toBe("FEMALE");
      expect(request.phone).toBe("0123456789"); // Should keep other defaults
    });
  });

  describe("compareProfileData", () => {
    it("should detect no changes", () => {
      const original = mockUsers[0];
      const updated = { ...mockUsers[0] };
      const result = compareProfileData(original, updated);
      
      expect(result.hasChanges).toBe(false);
      expect(Object.keys(result.differences)).toHaveLength(0);
    });

    it("should detect changes in specified fields", () => {
      const original = mockUsers[0];
      const updated = { ...mockUsers[0], name: "New Name" };
      const result = compareProfileData(original, updated);
      
      expect(result.hasChanges).toBe(true);
      expect(result.differences.name).toEqual({
        old: original.name,
        new: "New Name",
      });
    });

    it("should only compare specified fields", () => {
      const original = { name: "Old Name", email: "<EMAIL>" };
      const updated = { name: "New Name", email: "<EMAIL>" };
      const result = compareProfileData(original, updated, ["name"]);
      
      expect(result.hasChanges).toBe(true);
      expect(result.differences.name).toBeDefined();
      expect(result.differences.email).toBeUndefined();
    });
  });

  describe("formatDateForDisplay", () => {
    it("should format valid date string", () => {
      const result = formatDateForDisplay("1990-05-15");
      expect(result).toBe("15 tháng 5, 1990");
    });

    it("should format Date object", () => {
      const date = new Date("1990-05-15");
      const result = formatDateForDisplay(date);
      expect(result).toBe("15 tháng 5, 1990");
    });

    it("should handle null date", () => {
      const result = formatDateForDisplay(null);
      expect(result).toBe("");
    });

    it("should handle invalid date", () => {
      const result = formatDateForDisplay("invalid-date");
      expect(result).toBe("");
    });
  });

  describe("formatDateForInput", () => {
    it("should format valid date string", () => {
      const result = formatDateForInput("1990-05-15T00:00:00.000Z");
      expect(result).toBe("1990-05-15");
    });

    it("should format Date object", () => {
      const date = new Date("1990-05-15");
      const result = formatDateForInput(date);
      expect(result).toBe("1990-05-15");
    });

    it("should handle null date", () => {
      const result = formatDateForInput(null);
      expect(result).toBe("");
    });

    it("should handle invalid date", () => {
      const result = formatDateForInput("invalid-date");
      expect(result).toBe("");
    });
  });

  describe("formatGenderForDisplay", () => {
    it("should format MALE", () => {
      const result = formatGenderForDisplay("MALE");
      expect(result).toBe("Nam");
    });

    it("should format FEMALE", () => {
      const result = formatGenderForDisplay("FEMALE");
      expect(result).toBe("Nữ");
    });

    it("should format OTHER", () => {
      const result = formatGenderForDisplay("OTHER");
      expect(result).toBe("Khác");
    });

    it("should handle null gender", () => {
      const result = formatGenderForDisplay(null);
      expect(result).toBe("Chưa cập nhật");
    });

    it("should handle invalid gender", () => {
      const result = formatGenderForDisplay("INVALID");
      expect(result).toBe("Chưa cập nhật");
    });
  });

  describe("createMockProfileResponse", () => {
    it("should create successful response", () => {
      const user = mockUsers[0];
      const response = createMockProfileResponse(user, true);
      
      expect(response.status).toBe(200);
      expect(response.data.password).toBeUndefined();
      expect(response.data.id).toBe(user.id);
    });

    it("should create error response", () => {
      const user = mockUsers[0];
      const response = createMockProfileResponse(user, false, "Custom error");
      
      expect(response.status).toBe(400);
      expect(response.data.error).toBe("Custom error");
    });

    it("should use default error message", () => {
      const user = mockUsers[0];
      const response = createMockProfileResponse(user, false);
      
      expect(response.status).toBe(400);
      expect(response.data.error).toBe("Có lỗi xảy ra");
    });
  });

  describe("simulateApiDelay", () => {
    it("should resolve after specified delay", async () => {
      const startTime = Date.now();
      await simulateApiDelay(100);
      const endTime = Date.now();
      
      expect(endTime - startTime).toBeGreaterThanOrEqual(90); // Allow some tolerance
    });

    it("should use default delay", async () => {
      const startTime = Date.now();
      await simulateApiDelay();
      const endTime = Date.now();
      
      expect(endTime - startTime).toBeGreaterThanOrEqual(490); // Default 500ms with tolerance
    });
  });

  describe("testFormValidation", () => {
    it("should pass when errors match", () => {
      const formData = { name: "" };
      const expectedErrors = ["Họ tên là bắt buộc"];
      const result = testFormValidation(formData, expectedErrors);
      
      expect(result.passed).toBe(true);
      expect(result.actualErrors).toEqual(expectedErrors);
    });

    it("should fail when errors don't match", () => {
      const formData = { name: "Valid Name" };
      const expectedErrors = ["Họ tên là bắt buộc"];
      const result = testFormValidation(formData, expectedErrors);
      
      expect(result.passed).toBe(false);
      expect(result.actualErrors).toEqual([]);
    });
  });

  describe("generateRandomProfileData", () => {
    it("should generate valid profile data", () => {
      const data = generateRandomProfileData();
      
      expect(typeof data.name).toBe("string");
      expect(data.name.length).toBeGreaterThan(0);
      expect(typeof data.phone).toBe("string");
      expect(data.phone.length).toBe(10);
      expect(["MALE", "FEMALE", "OTHER"]).toContain(data.gender);
      expect(data.dateOfBirth).toMatch(/^\d{4}-\d{2}-\d{2}$/);
    });

    it("should generate different data on multiple calls", () => {
      const data1 = generateRandomProfileData();
      const data2 = generateRandomProfileData();
      
      // At least one field should be different (very high probability)
      const isDifferent = 
        data1.name !== data2.name ||
        data1.phone !== data2.phone ||
        data1.gender !== data2.gender ||
        data1.dateOfBirth !== data2.dateOfBirth;
      
      expect(isDifferent).toBe(true);
    });
  });

  describe("testProfileUpdateFlow", () => {
    it("should handle successful update", async () => {
      const originalProfile = mockUsers[0];
      const updateData = { name: "Updated Name" };
      const mockUpdateFn = jest.fn().mockResolvedValue({
        ...originalProfile,
        ...updateData,
      });
      
      const result = await testProfileUpdateFlow(originalProfile, updateData, mockUpdateFn);
      
      expect(result.success).toBe(true);
      expect(result.result.name).toBe("Updated Name");
      expect(result.changes.name).toEqual({
        old: originalProfile.name,
        new: "Updated Name",
      });
      expect(typeof result.duration).toBe("number");
    });

    it("should handle validation errors", async () => {
      const originalProfile = mockUsers[0];
      const updateData = { name: "" }; // Invalid
      const mockUpdateFn = jest.fn();
      
      const result = await testProfileUpdateFlow(originalProfile, updateData, mockUpdateFn);
      
      expect(result.success).toBe(false);
      expect(result.errors).toContain("Họ tên là bắt buộc");
      expect(mockUpdateFn).not.toHaveBeenCalled();
    });

    it("should handle update function errors", async () => {
      const originalProfile = mockUsers[0];
      const updateData = { name: "Valid Name" };
      const mockUpdateFn = jest.fn().mockRejectedValue(new Error("Update failed"));
      
      const result = await testProfileUpdateFlow(originalProfile, updateData, mockUpdateFn);
      
      expect(result.success).toBe(false);
      expect(result.error).toBe("Update failed");
      expect(typeof result.duration).toBe("number");
    });
  });
});
