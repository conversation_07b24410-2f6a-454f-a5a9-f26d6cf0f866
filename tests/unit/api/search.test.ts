/**
 * Search API Unit Tests
 * Kiểm tra unit cho search API endpoints
 */

import { GET } from "@/app/api/products/route";
import { NextRequest } from "next/server";
import { mockProducts } from "../../fixtures/mock-data";
import { filterProducts } from "../../helpers/search-helpers";

// Mock Prisma
jest.mock("@/lib/prisma", () => ({
  prisma: {
    product: {
      findMany: jest.fn(),
      count: jest.fn(),
    },
  },
}));

// Mock NextAuth
jest.mock("next-auth", () => ({
  getServerSession: jest.fn(),
}));

const { prisma } = require("@/lib/prisma");

describe("Search API", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("GET /api/products", () => {
    it("should return all products when no search query", async () => {
      // Mock Prisma responses
      prisma.product.findMany.mockResolvedValue(mockProducts);
      prisma.product.count.mockResolvedValue(mockProducts.length);

      const request = new NextRequest("http://localhost:3000/api/products");
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.products).toHaveLength(mockProducts.length);
      expect(data.pagination.total).toBe(mockProducts.length);
    });

    it("should filter products by search query", async () => {
      const searchQuery = "áo";
      const filteredProducts = filterProducts({ search: searchQuery });

      prisma.product.findMany.mockResolvedValue(filteredProducts);
      prisma.product.count.mockResolvedValue(filteredProducts.length);

      const request = new NextRequest(
        `http://localhost:3000/api/products?search=${searchQuery}`
      );
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.products).toHaveLength(filteredProducts.length);
      expect(prisma.product.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              { name: { contains: searchQuery, mode: "insensitive" } },
              { description: { contains: searchQuery, mode: "insensitive" } },
              { tags: { has: searchQuery } },
            ]),
          }),
        })
      );
    });

    it("should filter products by category", async () => {
      const categoryId = "cat-1";
      const filteredProducts = filterProducts({ category: categoryId });

      prisma.product.findMany.mockResolvedValue(filteredProducts);
      prisma.product.count.mockResolvedValue(filteredProducts.length);

      const request = new NextRequest(
        `http://localhost:3000/api/products?category=${categoryId}`
      );
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(prisma.product.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            categoryId: categoryId,
          }),
        })
      );
    });

    it("should filter products by featured status", async () => {
      const filteredProducts = filterProducts({ featured: true });

      prisma.product.findMany.mockResolvedValue(filteredProducts);
      prisma.product.count.mockResolvedValue(filteredProducts.length);

      const request = new NextRequest(
        "http://localhost:3000/api/products?featured=true"
      );
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(prisma.product.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            featured: true,
          }),
        })
      );
    });

    it("should handle pagination correctly", async () => {
      const page = 2;
      const limit = 5;
      const skip = (page - 1) * limit;

      prisma.product.findMany.mockResolvedValue(
        mockProducts.slice(skip, skip + limit)
      );
      prisma.product.count.mockResolvedValue(mockProducts.length);

      const request = new NextRequest(
        `http://localhost:3000/api/products?page=${page}&limit=${limit}`
      );
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(prisma.product.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          skip: skip,
          take: limit,
        })
      );
      expect(data.pagination.page).toBe(page);
      expect(data.pagination.limit).toBe(limit);
    });

    it("should handle sorting correctly", async () => {
      const sortBy = "price";
      const sortOrder = "asc";

      prisma.product.findMany.mockResolvedValue(mockProducts);
      prisma.product.count.mockResolvedValue(mockProducts.length);

      const request = new NextRequest(
        `http://localhost:3000/api/products?sortBy=${sortBy}&sortOrder=${sortOrder}`
      );
      const response = await GET(request);

      expect(response.status).toBe(200);
      expect(prisma.product.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          orderBy: {
            [sortBy]: sortOrder,
          },
        })
      );
    });

    it("should combine multiple filters", async () => {
      const searchQuery = "áo";
      const categoryId = "cat-1";
      const featured = true;

      const filteredProducts = filterProducts({
        search: searchQuery,
        category: categoryId,
        featured: featured,
      });

      prisma.product.findMany.mockResolvedValue(filteredProducts);
      prisma.product.count.mockResolvedValue(filteredProducts.length);

      const request = new NextRequest(
        `http://localhost:3000/api/products?search=${searchQuery}&category=${categoryId}&featured=${featured}`
      );
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(prisma.product.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            status: "ACTIVE",
            categoryId: categoryId,
            featured: featured,
            OR: expect.arrayContaining([
              { name: { contains: searchQuery, mode: "insensitive" } },
              { description: { contains: searchQuery, mode: "insensitive" } },
              { tags: { has: searchQuery } },
            ]),
          }),
        })
      );
    });

    it("should return empty results for non-existent search", async () => {
      const searchQuery = "nonexistent";

      prisma.product.findMany.mockResolvedValue([]);
      prisma.product.count.mockResolvedValue(0);

      const request = new NextRequest(
        `http://localhost:3000/api/products?search=${searchQuery}`
      );
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.products).toHaveLength(0);
      expect(data.pagination.total).toBe(0);
    });

    it("should handle database errors gracefully", async () => {
      prisma.product.findMany.mockRejectedValue(new Error("Database error"));

      const request = new NextRequest("http://localhost:3000/api/products");
      const response = await GET(request);

      expect(response.status).toBe(500);
    });

    it("should include product relationships", async () => {
      prisma.product.findMany.mockResolvedValue(mockProducts);
      prisma.product.count.mockResolvedValue(mockProducts.length);

      const request = new NextRequest("http://localhost:3000/api/products");
      const response = await GET(request);

      expect(response.status).toBe(200);
      expect(prisma.product.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          include: expect.objectContaining({
            category: expect.objectContaining({
              select: expect.objectContaining({
                id: true,
                name: true,
                slug: true,
              }),
            }),
            reviews: expect.objectContaining({
              select: expect.objectContaining({
                rating: true,
              }),
            }),
          }),
        })
      );
    });

    it("should calculate average rating correctly", async () => {
      const productsWithReviews = mockProducts.map((product) => ({
        ...product,
        reviews: [{ rating: 5 }, { rating: 4 }, { rating: 5 }],
      }));

      prisma.product.findMany.mockResolvedValue(productsWithReviews);
      prisma.product.count.mockResolvedValue(productsWithReviews.length);

      const request = new NextRequest("http://localhost:3000/api/products");
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      data.products.forEach((product: any) => {
        expect(product).toHaveProperty("avgRating");
        expect(product).toHaveProperty("reviewCount");
      });
    });

    it("should handle invalid pagination parameters", async () => {
      prisma.product.findMany.mockResolvedValue(mockProducts);
      prisma.product.count.mockResolvedValue(mockProducts.length);

      const request = new NextRequest(
        "http://localhost:3000/api/products?page=invalid&limit=invalid"
      );
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      // Should default to page 1, limit 12
      expect(data.pagination.page).toBe(1);
      expect(data.pagination.limit).toBe(12);
    });

    it("should handle case-insensitive search", async () => {
      const searchQuery = "ÁO";

      prisma.product.findMany.mockResolvedValue(mockProducts);
      prisma.product.count.mockResolvedValue(mockProducts.length);

      const request = new NextRequest(
        `http://localhost:3000/api/products?search=${searchQuery}`
      );
      const response = await GET(request);

      expect(response.status).toBe(200);
      expect(prisma.product.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              { name: { contains: searchQuery, mode: "insensitive" } },
              { description: { contains: searchQuery, mode: "insensitive" } },
              { tags: { has: searchQuery } },
            ]),
          }),
        })
      );
    });
  });
});
