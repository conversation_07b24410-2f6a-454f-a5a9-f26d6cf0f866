/**
 * Profile API Unit Tests
 * Kiểm tra unit cho profile API endpoints
 */

import { GET, PUT } from "@/app/api/profile/route";
import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { mockUsers, mockProfileUpdates } from "../../fixtures/mock-data";

// Mock Prisma
jest.mock("@/lib/prisma", () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  },
}));

// Mock NextAuth
jest.mock("next-auth", () => ({
  getServerSession: jest.fn(),
}));

const { prisma } = require("@/lib/prisma");
const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;

describe("Profile API", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("GET /api/profile", () => {
    it("should return user profile when authenticated", async () => {
      // Mock authenticated session
      mockGetServerSession.mockResolvedValue({
        user: { id: "user-1" },
      } as any);

      // Mock user with addresses
      const userWithAddresses = {
        ...mockUsers[0],
        addresses: [
          {
            id: "addr-1",
            userId: "user-1",
            fullName: "Test User",
            phone: "0123456789",
            address: "123 Test St",
            ward: "Test Ward",
            district: "Test District",
            province: "Test Province",
            isDefault: true,
          },
        ],
      };

      prisma.user.findUnique.mockResolvedValue(userWithAddresses);

      const request = new NextRequest("http://localhost:3000/api/profile");
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.id).toBe("user-1");
      expect(data.name).toBe(mockUsers[0].name);
      expect(data.email).toBe(mockUsers[0].email);
      expect(data.addresses).toBeDefined();
      expect(data.password).toBeUndefined(); // Password should be excluded
    });

    it("should return 401 when not authenticated", async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest("http://localhost:3000/api/profile");
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Vui lòng đăng nhập");
    });

    it("should return 404 when user not found", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: "non-existent" },
      } as any);

      prisma.user.findUnique.mockResolvedValue(null);

      const request = new NextRequest("http://localhost:3000/api/profile");
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe("Không tìm thấy người dùng");
    });

    it("should handle database errors", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: "user-1" },
      } as any);

      prisma.user.findUnique.mockRejectedValue(new Error("Database error"));

      const request = new NextRequest("http://localhost:3000/api/profile");
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Có lỗi xảy ra khi lấy thông tin cá nhân");
    });

    it("should include addresses ordered by default", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: "user-1" },
      } as any);

      prisma.user.findUnique.mockResolvedValue(mockUsers[0]);

      const request = new NextRequest("http://localhost:3000/api/profile");
      await GET(request);

      expect(prisma.user.findUnique).toHaveBeenCalledWith({
        where: { id: "user-1" },
        include: {
          addresses: {
            orderBy: {
              isDefault: "desc",
            },
          },
        },
      });
    });
  });

  describe("PUT /api/profile", () => {
    it("should update user profile successfully", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: "user-1" },
      } as any);

      const updateData = mockProfileUpdates.valid[0];
      const updatedUser = {
        ...mockUsers[0],
        ...updateData,
        dateOfBirth: new Date(updateData.dateOfBirth),
      };

      prisma.user.update.mockResolvedValue(updatedUser);

      const request = new NextRequest("http://localhost:3000/api/profile", {
        method: "PUT",
        body: JSON.stringify(updateData),
      });

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.name).toBe(updateData.name);
      expect(data.phone).toBe(updateData.phone);
      expect(data.gender).toBe(updateData.gender);
      expect(data.password).toBeUndefined(); // Password should be excluded
    });

    it("should return 401 when not authenticated", async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest("http://localhost:3000/api/profile", {
        method: "PUT",
        body: JSON.stringify(mockProfileUpdates.valid[0]),
      });

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe("Vui lòng đăng nhập");
    });

    it("should validate required name field", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: "user-1" },
      } as any);

      const invalidData = { ...mockProfileUpdates.valid[0], name: "" };

      const request = new NextRequest("http://localhost:3000/api/profile", {
        method: "PUT",
        body: JSON.stringify(invalidData),
      });

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Họ tên là bắt buộc");
    });

    it("should handle optional fields correctly", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: "user-1" },
      } as any);

      const updateData = {
        name: "Test User",
        phone: "",
        dateOfBirth: "",
        gender: "",
      };

      const updatedUser = {
        ...mockUsers[0],
        name: updateData.name,
      };

      prisma.user.update.mockResolvedValue(updatedUser);

      const request = new NextRequest("http://localhost:3000/api/profile", {
        method: "PUT",
        body: JSON.stringify(updateData),
      });

      const response = await PUT(request);

      expect(response.status).toBe(200);
      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: "user-1" },
        data: {
          name: updateData.name,
        },
        include: {
          addresses: {
            orderBy: {
              isDefault: "desc",
            },
          },
        },
      });
    });

    it("should update all fields when provided", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: "user-1" },
      } as any);

      const updateData = mockProfileUpdates.valid[0];
      const updatedUser = {
        ...mockUsers[0],
        ...updateData,
        dateOfBirth: new Date(updateData.dateOfBirth),
      };

      prisma.user.update.mockResolvedValue(updatedUser);

      const request = new NextRequest("http://localhost:3000/api/profile", {
        method: "PUT",
        body: JSON.stringify(updateData),
      });

      const response = await PUT(request);

      expect(response.status).toBe(200);
      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: "user-1" },
        data: {
          name: updateData.name,
          phone: updateData.phone,
          dateOfBirth: new Date(updateData.dateOfBirth),
          gender: updateData.gender,
        },
        include: {
          addresses: {
            orderBy: {
              isDefault: "desc",
            },
          },
        },
      });
    });

    it("should validate gender enum", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: "user-1" },
      } as any);

      const invalidData = {
        name: "Test User",
        gender: "INVALID_GENDER",
      };

      const request = new NextRequest("http://localhost:3000/api/profile", {
        method: "PUT",
        body: JSON.stringify(invalidData),
      });

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Invalid enum value");
    });

    it("should handle date parsing correctly", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: "user-1" },
      } as any);

      const updateData = {
        name: "Test User",
        dateOfBirth: "1990-05-15",
      };

      const updatedUser = {
        ...mockUsers[0],
        ...updateData,
        dateOfBirth: new Date("1990-05-15"),
      };

      prisma.user.update.mockResolvedValue(updatedUser);

      const request = new NextRequest("http://localhost:3000/api/profile", {
        method: "PUT",
        body: JSON.stringify(updateData),
      });

      const response = await PUT(request);

      expect(response.status).toBe(200);
      expect(prisma.user.update).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            dateOfBirth: new Date("1990-05-15"),
          }),
        })
      );
    });

    it("should handle database update errors", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: "user-1" },
      } as any);

      prisma.user.update.mockRejectedValue(new Error("Database error"));

      const request = new NextRequest("http://localhost:3000/api/profile", {
        method: "PUT",
        body: JSON.stringify(mockProfileUpdates.valid[0]),
      });

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Có lỗi xảy ra khi cập nhật thông tin cá nhân");
    });

    it("should handle invalid JSON body", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: "user-1" },
      } as any);

      const request = new NextRequest("http://localhost:3000/api/profile", {
        method: "PUT",
        body: "invalid json",
      });

      const response = await PUT(request);

      expect(response.status).toBe(500);
    });

    it("should exclude password from response", async () => {
      mockGetServerSession.mockResolvedValue({
        user: { id: "user-1" },
      } as any);

      const updatedUser = {
        ...mockUsers[0],
        password: "hashed_password",
      };

      prisma.user.update.mockResolvedValue(updatedUser);

      const request = new NextRequest("http://localhost:3000/api/profile", {
        method: "PUT",
        body: JSON.stringify(mockProfileUpdates.valid[0]),
      });

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.password).toBeUndefined();
      expect(data.id).toBe(mockUsers[0].id);
      expect(data.name).toBeDefined();
      expect(data.email).toBeDefined();
    });
  });
});
