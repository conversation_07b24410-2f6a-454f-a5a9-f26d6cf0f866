/**
 * Test setup verification
 * <PERSON><PERSON><PERSON> tra xem môi trường testing đã được cấu hình đúng chưa
 */

describe('Testing Setup', () => {
  it('should have Jest configured correctly', () => {
    expect(true).toBe(true)
  })

  it('should have environment variables set', () => {
    expect(process.env.NODE_ENV).toBe('test')
    expect(process.env.NEXTAUTH_SECRET).toBeDefined()
    expect(process.env.DATABASE_URL).toBeDefined()
  })

  it('should have global mocks available', () => {
    expect(global.ResizeObserver).toBeDefined()
    expect(global.IntersectionObserver).toBeDefined()
    expect(window.matchMedia).toBeDefined()
    expect(window.scrollTo).toBeDefined()
  })

  it('should mock Next.js modules', () => {
    const { useRouter } = require('next/navigation')
    const router = useRouter()
    
    expect(router.push).toBeDefined()
    expect(router.replace).toBeDefined()
    expect(router.refresh).toBeDefined()
  })
})
