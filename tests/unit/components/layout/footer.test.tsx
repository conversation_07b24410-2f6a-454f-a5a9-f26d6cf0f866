/**
 * Footer component unit tests
 * Ki<PERSON>m tra unit cho Footer component
 */

import React from "react";
import { render, screen } from "../../../helpers/test-utils";
import { Footer } from "@/components/layout/footer";

describe("Footer Component", () => {
  describe("Rendering", () => {
    it("should render footer with company info", () => {
      render(<Footer />);

      // Check company logo and name
      const companyName = screen.getByText("NS Shop");
      expect(companyName).toBeInTheDocument();

      // Check company description
      const description = screen.getByText(
        /Khám phá xu hướng thời trang mới nhất/
      );
      expect(description).toBeInTheDocument();
    });

    it("should render all footer sections", () => {
      render(<Footer />);

      // Check section headings
      expect(screen.getByText("Về NS Shop")).toBeInTheDocument();
      expect(screen.getByText("Hỗ trợ khách hàng")).toBeInTheDocument();
      expect(screen.getByText("Chính sách")).toBeInTheDocument();
      expect(screen.getByText("Thông tin liên hệ")).toBeInTheDocument();
    });

    it("should render social media links", () => {
      render(<Footer />);

      // Check for social media links (they should be links with proper hrefs)
      const socialLinks = screen
        .getAllByRole("link")
        .filter(
          (link) =>
            link.getAttribute("href")?.includes("facebook") ||
            link.getAttribute("href")?.includes("instagram") ||
            link.getAttribute("href")?.includes("twitter") ||
            link.getAttribute("href")?.includes("youtube")
        );

      expect(socialLinks.length).toBeGreaterThan(0);
    });

    it("should render contact information", () => {
      render(<Footer />);

      // Check contact details
      expect(
        screen.getByText(/123 Đường ABC, Quận 1, TP.HCM/)
      ).toBeInTheDocument();
      expect(screen.getByText(/\+84 123 456 789/)).toBeInTheDocument();
      expect(screen.getByText(/<EMAIL>/)).toBeInTheDocument();
    });
  });

  describe("Navigation Links", () => {
    it("should render about us section links", () => {
      render(<Footer />);

      // Check "Về NS Shop" section links
      expect(screen.getByRole("link", { name: /giới thiệu/i })).toHaveAttribute(
        "href",
        "/about"
      );
      expect(screen.getByRole("link", { name: /tuyển dụng/i })).toHaveAttribute(
        "href",
        "/careers"
      );
      expect(screen.getByRole("link", { name: /tin tức/i })).toHaveAttribute(
        "href",
        "/news"
      );
      expect(screen.getByRole("link", { name: /liên hệ/i })).toHaveAttribute(
        "href",
        "/contact"
      );
    });

    it("should render customer support section links", () => {
      render(<Footer />);

      // Check "Hỗ trợ khách hàng" section links
      expect(
        screen.getByRole("link", { name: /hướng dẫn mua hàng/i })
      ).toHaveAttribute("href", "/guide");
      expect(
        screen.getByRole("link", { name: /hướng dẫn thanh toán/i })
      ).toHaveAttribute("href", "/payment-guide");
      expect(
        screen.getByRole("link", { name: /chính sách đổi trả/i })
      ).toHaveAttribute("href", "/return-policy");
      expect(
        screen.getByRole("link", { name: /câu hỏi thường gặp/i })
      ).toHaveAttribute("href", "/faq");
    });

    it("should render policy section links", () => {
      render(<Footer />);

      // Check "Chính sách" section links
      expect(
        screen.getByRole("link", { name: /chính sách bảo mật/i })
      ).toHaveAttribute("href", "/privacy");
      expect(
        screen.getByRole("link", { name: /điều khoản sử dụng/i })
      ).toHaveAttribute("href", "/terms");
      expect(
        screen.getByRole("link", { name: /chính sách cookie/i })
      ).toHaveAttribute("href", "/cookies");
      expect(
        screen.getByRole("link", { name: /chính sách vận chuyển/i })
      ).toHaveAttribute("href", "/shipping");
    });

    it("should render bottom bar links", () => {
      render(<Footer />);

      // Check bottom bar links
      const bottomLinks = screen.getAllByRole("link").filter((link) => {
        const href = link.getAttribute("href");
        return href === "/terms" || href === "/privacy" || href === "/cookies";
      });

      expect(bottomLinks.length).toBe(3);
    });
  });

  describe("Social Media Links", () => {
    it("should render social media links with icons", () => {
      render(<Footer />);

      // Get all social media links (they have # href and contain SVG icons)
      const socialLinks = screen
        .getAllByRole("link")
        .filter(
          (link) =>
            link.getAttribute("href") === "#" && link.querySelector("svg")
        );

      expect(socialLinks.length).toBe(4); // Facebook, Instagram, Twitter, YouTube

      // Check each link has proper href
      socialLinks.forEach((link) => {
        expect(link).toHaveAttribute("href", "#");
        expect(link).toHaveClass(
          "text-muted-foreground",
          "hover:text-primary",
          "transition-colors"
        );
      });
    });

    it("should render Facebook icon", () => {
      render(<Footer />);

      // Find Facebook icon by SVG path
      const facebookIcon = screen.getByRole("link", {
        name: "", // Icon-only links don't have accessible names
      });
      expect(facebookIcon).toBeInTheDocument();
    });

    it("should render social media icons with proper styling", () => {
      render(<Footer />);

      const socialContainer = screen
        .getByText("NS Shop")
        .parentElement?.querySelector(".flex.space-x-4");
      expect(socialContainer).toBeInTheDocument();

      const socialLinks = socialContainer?.querySelectorAll("a");
      expect(socialLinks?.length).toBe(4);

      socialLinks?.forEach((link) => {
        const svg = link.querySelector("svg");
        expect(svg).toBeInTheDocument();
        expect(svg).toHaveClass("h-5", "w-5");
      });
    });
  });

  describe("Contact Information", () => {
    it("should display address with map icon", () => {
      render(<Footer />);

      const address = screen.getByText("123 Đường ABC, Quận 1, TP.HCM");
      expect(address).toBeInTheDocument();

      // Check if address is in a container with map icon
      const addressContainer = address.closest("div");
      expect(addressContainer).toBeInTheDocument();
    });

    it("should display phone number with phone icon", () => {
      render(<Footer />);

      const phone = screen.getByText("+84 123 456 789");
      expect(phone).toBeInTheDocument();

      // Check if phone is in a container with phone icon
      const phoneContainer = phone.closest("div");
      expect(phoneContainer).toBeInTheDocument();
    });

    it("should display email with mail icon", () => {
      render(<Footer />);

      const email = screen.getByText("<EMAIL>");
      expect(email).toBeInTheDocument();

      // Check if email is in a container with mail icon
      const emailContainer = email.closest("div");
      expect(emailContainer).toBeInTheDocument();
    });
  });

  describe("Copyright and Legal", () => {
    it("should display copyright notice", () => {
      render(<Footer />);

      const copyright = screen.getByText(
        /© 2024 NS Shop. Tất cả quyền được bảo lưu./
      );
      expect(copyright).toBeInTheDocument();
    });

    it("should render legal links in bottom bar", () => {
      render(<Footer />);

      // Check for legal links in bottom section
      const termsLink = screen.getAllByRole("link", {
        name: /điều khoản sử dụng/i,
      });
      expect(termsLink.length).toBeGreaterThan(0);

      const privacyLink = screen.getAllByRole("link", {
        name: /chính sách bảo mật/i,
      });
      expect(privacyLink.length).toBeGreaterThan(0);

      const cookiesLink = screen.getAllByRole("link", {
        name: /chính sách cookie/i,
      });
      expect(cookiesLink.length).toBeGreaterThan(0);
    });
  });

  describe("Structure and Layout", () => {
    it("should have proper footer structure", () => {
      render(<Footer />);

      const footer = screen.getByRole("contentinfo");
      expect(footer).toBeInTheDocument();
      expect(footer).toHaveClass("bg-muted/50", "border-t");
    });

    it("should have responsive grid layout", () => {
      render(<Footer />);

      const footer = screen.getByRole("contentinfo");
      const container = footer.querySelector(".container");
      expect(container).toBeInTheDocument();

      const grid = container?.querySelector(".grid");
      expect(grid).toBeInTheDocument();
      expect(grid).toHaveClass(
        "grid-cols-1",
        "md:grid-cols-2",
        "lg:grid-cols-4"
      );
    });

    it("should have proper spacing and padding", () => {
      render(<Footer />);

      const footer = screen.getByRole("contentinfo");
      const container = footer.querySelector(".container");
      expect(container).toHaveClass("mx-auto", "px-4", "py-12");
    });
  });

  describe("Accessibility", () => {
    it("should have proper semantic structure", () => {
      render(<Footer />);

      const footer = screen.getByRole("contentinfo");
      expect(footer).toBeInTheDocument();

      // Check for headings (3 main sections + 1 h4)
      const h3Headings = screen.getAllByRole("heading", { level: 3 });
      expect(h3Headings.length).toBe(3); // Three main sections with h3

      const h4Headings = screen.getAllByRole("heading", { level: 4 });
      expect(h4Headings.length).toBe(1); // One h4 for newsletter
    });

    it("should have accessible text links", () => {
      render(<Footer />);

      const textLinks = screen.getAllByRole("link").filter((link) => {
        const text = link.textContent?.trim();
        return text && text.length > 0;
      });

      // Text links should have accessible names
      textLinks.forEach((link) => {
        expect(link).toHaveAccessibleName();
      });
    });

    it("should have icon links with proper structure", () => {
      render(<Footer />);

      const iconLinks = screen
        .getAllByRole("link")
        .filter(
          (link) =>
            link.getAttribute("href") === "#" && link.querySelector("svg")
        );

      expect(iconLinks.length).toBe(4); // Social media icons

      // Icon links should have SVG elements
      iconLinks.forEach((link) => {
        const svg = link.querySelector("svg");
        expect(svg).toBeInTheDocument();
      });
    });

    it("should have proper external link attributes", () => {
      render(<Footer />);

      // Check external links have proper security attributes
      const externalLinks = screen
        .getAllByRole("link")
        .filter((link) => link.getAttribute("target") === "_blank");

      externalLinks.forEach((link) => {
        expect(link).toHaveAttribute("rel", "noopener noreferrer");
      });
    });
  });
});
