/**
 * Profile Form Component Unit Tests
 * Ki<PERSON>m tra unit cho profile form component
 */

import React from "react";
import { render, screen, fireEvent, waitFor } from "../../../helpers/test-utils";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import ProfilePage from "@/app/profile/page";
import { mockUsers } from "../../../fixtures/mock-data";

// Mock useSession
jest.mock("next-auth/react", () => ({
  useSession: jest.fn(),
}));

// Mock useRouter
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
}));

// Mock fetch
global.fetch = jest.fn();

// Mock toast
jest.mock("sonner", () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

const mockPush = jest.fn();
const mockSession = useSession as jest.MockedFunction<typeof useSession>;
const mockRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

describe("Profile Form Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockRouter.mockReturnValue({
      push: mockPush,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    } as any);

    // Mock successful profile fetch
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => mockUsers[0],
    } as Response);
  });

  describe("Authentication", () => {
    it("should redirect to signin when not authenticated", () => {
      mockSession.mockReturnValue({ data: null } as any);
      
      render(<ProfilePage />);
      
      expect(mockPush).toHaveBeenCalledWith("/auth/signin");
    });

    it("should render profile page when authenticated", async () => {
      mockSession.mockReturnValue({
        data: { user: mockUsers[0] },
      } as any);
      
      render(<ProfilePage />);
      
      await waitFor(() => {
        expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      });
    });
  });

  describe("Profile Display", () => {
    beforeEach(() => {
      mockSession.mockReturnValue({
        data: { user: mockUsers[0] },
      } as any);
    });

    it("should display user information", async () => {
      render(<ProfilePage />);
      
      await waitFor(() => {
        expect(screen.getByText(mockUsers[0].name)).toBeInTheDocument();
        expect(screen.getByText(mockUsers[0].email)).toBeInTheDocument();
        expect(screen.getByText(mockUsers[0].phone)).toBeInTheDocument();
      });
    });

    it("should display formatted date of birth", async () => {
      render(<ProfilePage />);
      
      await waitFor(() => {
        // Should display formatted date
        expect(screen.getByText(/15 tháng 5, 1990/)).toBeInTheDocument();
      });
    });

    it("should display formatted gender", async () => {
      render(<ProfilePage />);
      
      await waitFor(() => {
        expect(screen.getByText("Nam")).toBeInTheDocument();
      });
    });

    it("should show edit button", async () => {
      render(<ProfilePage />);
      
      await waitFor(() => {
        const editButton = screen.getByText("Chỉnh sửa");
        expect(editButton).toBeInTheDocument();
      });
    });
  });

  describe("Profile Editing", () => {
    beforeEach(() => {
      mockSession.mockReturnValue({
        data: { user: mockUsers[0] },
      } as any);
    });

    it("should enter edit mode when edit button clicked", async () => {
      render(<ProfilePage />);
      
      await waitFor(() => {
        const editButton = screen.getByText("Chỉnh sửa");
        fireEvent.click(editButton);
      });
      
      // Should show form inputs
      expect(screen.getByDisplayValue(mockUsers[0].name)).toBeInTheDocument();
      expect(screen.getByDisplayValue(mockUsers[0].phone)).toBeInTheDocument();
    });

    it("should show save and cancel buttons in edit mode", async () => {
      render(<ProfilePage />);
      
      await waitFor(() => {
        const editButton = screen.getByText("Chỉnh sửa");
        fireEvent.click(editButton);
      });
      
      expect(screen.getByText("Lưu thay đổi")).toBeInTheDocument();
      expect(screen.getByText("Hủy")).toBeInTheDocument();
    });

    it("should update form values when typing", async () => {
      render(<ProfilePage />);
      
      await waitFor(() => {
        const editButton = screen.getByText("Chỉnh sửa");
        fireEvent.click(editButton);
      });
      
      const nameInput = screen.getByDisplayValue(mockUsers[0].name);
      fireEvent.change(nameInput, { target: { value: "New Name" } });
      
      expect(nameInput).toHaveValue("New Name");
    });

    it("should cancel editing when cancel button clicked", async () => {
      render(<ProfilePage />);
      
      await waitFor(() => {
        const editButton = screen.getByText("Chỉnh sửa");
        fireEvent.click(editButton);
      });
      
      // Change a value
      const nameInput = screen.getByDisplayValue(mockUsers[0].name);
      fireEvent.change(nameInput, { target: { value: "New Name" } });
      
      // Cancel
      const cancelButton = screen.getByText("Hủy");
      fireEvent.click(cancelButton);
      
      // Should exit edit mode and restore original values
      await waitFor(() => {
        expect(screen.getByText(mockUsers[0].name)).toBeInTheDocument();
        expect(screen.queryByDisplayValue("New Name")).not.toBeInTheDocument();
      });
    });
  });

  describe("Profile Update", () => {
    beforeEach(() => {
      mockSession.mockReturnValue({
        data: { user: mockUsers[0] },
      } as any);
    });

    it("should submit form with updated data", async () => {
      const updatedUser = { ...mockUsers[0], name: "Updated Name" };
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockUsers[0],
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => updatedUser,
        } as Response);
      
      render(<ProfilePage />);
      
      await waitFor(() => {
        const editButton = screen.getByText("Chỉnh sửa");
        fireEvent.click(editButton);
      });
      
      // Update name
      const nameInput = screen.getByDisplayValue(mockUsers[0].name);
      fireEvent.change(nameInput, { target: { value: "Updated Name" } });
      
      // Submit form
      const saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);
      
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith("/api/profile", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name: "Updated Name",
            phone: mockUsers[0].phone,
            dateOfBirth: "1990-05-15",
            gender: mockUsers[0].gender,
          }),
        });
      });
    });

    it("should handle successful update", async () => {
      const { toast } = require("sonner");
      const updatedUser = { ...mockUsers[0], name: "Updated Name" };
      
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockUsers[0],
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => updatedUser,
        } as Response);
      
      render(<ProfilePage />);
      
      await waitFor(() => {
        const editButton = screen.getByText("Chỉnh sửa");
        fireEvent.click(editButton);
      });
      
      const nameInput = screen.getByDisplayValue(mockUsers[0].name);
      fireEvent.change(nameInput, { target: { value: "Updated Name" } });
      
      const saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);
      
      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith("Cập nhật thông tin thành công");
      });
    });

    it("should handle update error", async () => {
      const { toast } = require("sonner");
      
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockUsers[0],
        } as Response)
        .mockResolvedValueOnce({
          ok: false,
          json: async () => ({ error: "Update failed" }),
        } as Response);
      
      render(<ProfilePage />);
      
      await waitFor(() => {
        const editButton = screen.getByText("Chỉnh sửa");
        fireEvent.click(editButton);
      });
      
      const saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);
      
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith("Update failed");
      });
    });

    it("should handle network error", async () => {
      const { toast } = require("sonner");
      
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockUsers[0],
        } as Response)
        .mockRejectedValueOnce(new Error("Network error"));
      
      render(<ProfilePage />);
      
      await waitFor(() => {
        const editButton = screen.getByText("Chỉnh sửa");
        fireEvent.click(editButton);
      });
      
      const saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);
      
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith("Có lỗi xảy ra khi cập nhật thông tin");
      });
    });
  });

  describe("Form Validation", () => {
    beforeEach(() => {
      mockSession.mockReturnValue({
        data: { user: mockUsers[0] },
      } as any);
    });

    it("should handle empty name validation", async () => {
      const { toast } = require("sonner");
      
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockUsers[0],
        } as Response)
        .mockResolvedValueOnce({
          ok: false,
          json: async () => ({ error: "Họ tên là bắt buộc" }),
        } as Response);
      
      render(<ProfilePage />);
      
      await waitFor(() => {
        const editButton = screen.getByText("Chỉnh sửa");
        fireEvent.click(editButton);
      });
      
      // Clear name
      const nameInput = screen.getByDisplayValue(mockUsers[0].name);
      fireEvent.change(nameInput, { target: { value: "" } });
      
      const saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);
      
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith("Họ tên là bắt buộc");
      });
    });

    it("should handle date input correctly", async () => {
      render(<ProfilePage />);
      
      await waitFor(() => {
        const editButton = screen.getByText("Chỉnh sửa");
        fireEvent.click(editButton);
      });
      
      const dateInput = screen.getByDisplayValue("1990-05-15");
      expect(dateInput).toHaveAttribute("type", "date");
      
      fireEvent.change(dateInput, { target: { value: "1995-12-25" } });
      expect(dateInput).toHaveValue("1995-12-25");
    });

    it("should handle gender selection", async () => {
      render(<ProfilePage />);
      
      await waitFor(() => {
        const editButton = screen.getByText("Chỉnh sửa");
        fireEvent.click(editButton);
      });
      
      const genderSelect = screen.getByDisplayValue("MALE");
      fireEvent.change(genderSelect, { target: { value: "FEMALE" } });
      expect(genderSelect).toHaveValue("FEMALE");
    });
  });

  describe("Loading States", () => {
    it("should show loading state initially", () => {
      mockSession.mockReturnValue({
        data: { user: mockUsers[0] },
      } as any);
      
      // Mock pending fetch
      mockFetch.mockReturnValue(new Promise(() => {}));
      
      render(<ProfilePage />);
      
      expect(screen.getByText("Đang tải...")).toBeInTheDocument();
    });

    it("should handle profile fetch error", async () => {
      const { toast } = require("sonner");
      mockSession.mockReturnValue({
        data: { user: mockUsers[0] },
      } as any);
      
      mockFetch.mockResolvedValue({
        ok: false,
        json: async () => ({ error: "Failed to fetch profile" }),
      } as Response);
      
      render(<ProfilePage />);
      
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith("Có lỗi xảy ra khi tải thông tin cá nhân");
      });
    });
  });
});
