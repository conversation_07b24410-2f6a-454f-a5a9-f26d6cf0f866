/**
 * Search Input Component Unit Tests
 * Kiểm tra unit cho search input component trong header
 */

import React from "react";
import {
  render,
  screen,
  fireEvent,
  waitFor,
} from "../../../helpers/test-utils";
import { useRouter } from "next/navigation";
import { Header } from "@/components/layout/header";

// Mock useRouter
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
}));

// Mock useSession
jest.mock("next-auth/react", () => ({
  useSession: jest.fn(() => ({ data: null })),
}));

// Mock useSettingsContext
jest.mock("@/contexts/SettingsContext", () => ({
  useSettingsContext: jest.fn(() => ({
    settings: {
      siteName: "NS Shop",
    },
  })),
}));

const mockPush = jest.fn();
const mockRouter = useRouter as jest.MockedFunction<typeof useRouter>;

describe("Search Input Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockRouter.mockReturnValue({
      push: mockPush,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    } as any);
  });

  describe("Desktop Search", () => {
    it("should render search input in desktop view", () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      expect(searchInput).toBeInTheDocument();
      expect(searchInput).toHaveClass("w-full", "pl-10", "pr-4", "py-2");
    });

    it("should update search query when typing", () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");

      fireEvent.change(searchInput, { target: { value: "áo thun" } });

      expect(searchInput).toHaveValue("áo thun");
    });

    it("should navigate to search page on form submit", async () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");

      fireEvent.change(searchInput, { target: { value: "áo thun" } });
      fireEvent.submit(searchInput.closest("form")!);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith("/search?search=%C3%A1o%20thun");
      });
    });

    it("should clear search input after submit", async () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");

      fireEvent.change(searchInput, { target: { value: "áo thun" } });
      fireEvent.submit(searchInput.closest("form")!);

      await waitFor(() => {
        expect(searchInput).toHaveValue("");
      });
    });

    it("should not navigate with empty search query", async () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");

      fireEvent.submit(searchInput.closest("form")!);

      await waitFor(() => {
        expect(mockPush).not.toHaveBeenCalled();
      });
    });

    it("should not navigate with whitespace-only search query", async () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");

      fireEvent.change(searchInput, { target: { value: "   " } });
      fireEvent.submit(searchInput.closest("form")!);

      await waitFor(() => {
        expect(mockPush).not.toHaveBeenCalled();
      });
    });

    it("should encode special characters in search query", async () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");

      fireEvent.change(searchInput, { target: { value: "áo & quần" } });
      fireEvent.submit(searchInput.closest("form")!);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith(
          "/search?search=%C3%A1o%20%26%20qu%E1%BA%A7n"
        );
      });
    });
  });

  describe("Mobile Search", () => {
    it("should render mobile search button", () => {
      render(<Header />);

      // Find mobile search button by class since it doesn't have accessible name
      const buttons = screen.getAllByRole("button");
      const mobileSearchButton = buttons.find(
        (button) =>
          button.classList.contains("md:hidden") &&
          button.querySelector("svg.lucide-search")
      );

      expect(mobileSearchButton).toBeInTheDocument();
      expect(mobileSearchButton).toHaveClass("md:hidden");
    });

    it("should open mobile menu when search button clicked", () => {
      render(<Header />);

      // Find mobile search button
      const buttons = screen.getAllByRole("button");
      const mobileSearchButton = buttons.find(
        (button) =>
          button.classList.contains("md:hidden") &&
          button.querySelector("svg.lucide-search")
      );

      fireEvent.click(mobileSearchButton!);

      // Mobile menu should be visible - check for mobile-specific navigation
      const mobileMenus = screen.getAllByText("Trang chủ");
      expect(mobileMenus.length).toBeGreaterThan(1); // Desktop + Mobile
    });

    it("should render search input in mobile menu", () => {
      render(<Header />);

      // Open mobile menu by clicking menu button (not search button)
      const buttons = screen.getAllByRole("button");
      const menuButton = buttons.find(
        (button) =>
          button.classList.contains("md:hidden") &&
          button.querySelector("svg.lucide-menu")
      );

      fireEvent.click(menuButton!);

      // Check for mobile search input
      const mobileSearchInputs = screen.getAllByPlaceholderText(
        "Tìm kiếm sản phẩm..."
      );
      expect(mobileSearchInputs.length).toBeGreaterThan(1); // Desktop + Mobile
    });

    it("should navigate from mobile search", async () => {
      render(<Header />);

      // Open mobile menu
      const buttons = screen.getAllByRole("button");
      const menuButton = buttons.find(
        (button) =>
          button.classList.contains("md:hidden") &&
          button.querySelector("svg.lucide-menu")
      );

      fireEvent.click(menuButton!);

      // Find mobile search input and button
      const searchButton = screen.getByText("Tìm");
      const mobileSearchInputs = screen.getAllByPlaceholderText(
        "Tìm kiếm sản phẩm..."
      );
      const mobileSearchInput = mobileSearchInputs[1]; // Second one is mobile

      fireEvent.change(mobileSearchInput, { target: { value: "quần jeans" } });
      fireEvent.click(searchButton);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith(
          "/search?search=qu%E1%BA%A7n%20jeans"
        );
      });
    });

    it("should handle Enter key in mobile search", async () => {
      render(<Header />);

      // Open mobile menu
      const buttons = screen.getAllByRole("button");
      const menuButton = buttons.find(
        (button) =>
          button.classList.contains("md:hidden") &&
          button.querySelector("svg.lucide-menu")
      );

      fireEvent.click(menuButton!);

      // Find mobile search input
      const mobileSearchInputs = screen.getAllByPlaceholderText(
        "Tìm kiếm sản phẩm..."
      );
      const mobileSearchInput = mobileSearchInputs[1]; // Second one is mobile

      fireEvent.change(mobileSearchInput, { target: { value: "áo sơ mi" } });
      fireEvent.keyDown(mobileSearchInput, { key: "Enter", code: "Enter" });

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith(
          "/search?search=%C3%A1o%20s%C6%A1%20mi"
        );
      });
    });

    it("should close mobile menu after search", async () => {
      render(<Header />);

      // Open mobile menu
      const buttons = screen.getAllByRole("button");
      const menuButton = buttons.find(
        (button) =>
          button.classList.contains("md:hidden") &&
          button.querySelector("svg.lucide-menu")
      );

      fireEvent.click(menuButton!);

      // Perform search
      const searchButton = screen.getByText("Tìm");
      const mobileSearchInputs = screen.getAllByPlaceholderText(
        "Tìm kiếm sản phẩm..."
      );
      const mobileSearchInput = mobileSearchInputs[1];

      fireEvent.change(mobileSearchInput, { target: { value: "test" } });
      fireEvent.click(searchButton);

      await waitFor(() => {
        // Mobile menu should be closed (navigation links not visible)
        expect(screen.queryByText("Trang chủ")).not.toBeInTheDocument();
      });
    });
  });

  describe("Search Input Validation", () => {
    it("should handle very long search queries", async () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      const longQuery = "a".repeat(200);

      fireEvent.change(searchInput, { target: { value: longQuery } });
      fireEvent.submit(searchInput.closest("form")!);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith(
          `/search?search=${encodeURIComponent(longQuery)}`
        );
      });
    });

    it("should handle special characters and emojis", async () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      const specialQuery = "áo 👕 & quần 👖";

      fireEvent.change(searchInput, { target: { value: specialQuery } });
      fireEvent.submit(searchInput.closest("form")!);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith(
          `/search?search=${encodeURIComponent(specialQuery)}`
        );
      });
    });

    it("should trim whitespace from search query", async () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");

      fireEvent.change(searchInput, { target: { value: "  áo thun  " } });
      fireEvent.submit(searchInput.closest("form")!);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith("/search?search=%C3%A1o%20thun");
      });
    });
  });

  describe("Accessibility", () => {
    it("should have proper ARIA labels", () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      expect(searchInput).toHaveAttribute("type", "text");
      expect(searchInput).toHaveAttribute(
        "placeholder",
        "Tìm kiếm sản phẩm..."
      );
    });

    it("should be keyboard accessible", () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");

      // Should be focusable
      searchInput.focus();
      expect(document.activeElement).toBe(searchInput);
    });

    it("should have proper focus styles", () => {
      render(<Header />);

      const searchInput = screen.getByPlaceholderText("Tìm kiếm sản phẩm...");
      expect(searchInput).toHaveClass(
        "focus:outline-none",
        "focus:ring-2",
        "focus:ring-ring"
      );
    });
  });
});
