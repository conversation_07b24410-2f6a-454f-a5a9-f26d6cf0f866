/**
 * Button component unit tests
 * Kiểm tra unit cho component Button
 */

import React from 'react'
import { render, screen, fireEvent } from '../../helpers/test-utils'

// Mock Button component since we don't have the actual component yet
const Button = ({ children, onClick, disabled, className, ...props }: any) => (
  <button
    onClick={onClick}
    disabled={disabled}
    className={className}
    data-testid="button"
    {...props}
  >
    {children}
  </button>
)

describe('Button Component', () => {
  it('should render button with text', () => {
    render(<Button>Click me</Button>)
    
    const button = screen.getByTestId('button')
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('Click me')
  })

  it('should handle click events', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    const button = screen.getByTestId('button')
    fireEvent.click(button)
    
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('should be disabled when disabled prop is true', () => {
    render(<Button disabled>Disabled button</Button>)
    
    const button = screen.getByTestId('button')
    expect(button).toBeDisabled()
  })

  it('should not call onClick when disabled', () => {
    const handleClick = jest.fn()
    render(
      <Button onClick={handleClick} disabled>
        Disabled button
      </Button>
    )
    
    const button = screen.getByTestId('button')
    fireEvent.click(button)
    
    expect(handleClick).not.toHaveBeenCalled()
  })

  it('should apply custom className', () => {
    render(<Button className="custom-class">Styled button</Button>)
    
    const button = screen.getByTestId('button')
    expect(button).toHaveClass('custom-class')
  })

  it('should pass through additional props', () => {
    render(<Button type="submit" id="submit-btn">Submit</Button>)
    
    const button = screen.getByTestId('button')
    expect(button).toHaveAttribute('type', 'submit')
    expect(button).toHaveAttribute('id', 'submit-btn')
  })
})
