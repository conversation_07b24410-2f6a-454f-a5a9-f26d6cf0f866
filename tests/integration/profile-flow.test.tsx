/**
 * Profile Flow Integration Tests
 * Kiểm tra integration cho toàn bộ flow update profile từ form đến API
 */

import React from "react";
import { render, screen, fireEvent, waitFor } from "../helpers/test-utils";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import ProfilePage from "@/app/profile/page";
import { mockUsers, mockApiResponses } from "../fixtures/mock-data";
import { server } from "../mocks/server";
import { http, HttpResponse } from "msw";

// Mock useSession
jest.mock("next-auth/react", () => ({
  useSession: jest.fn(),
}));

// Mock useRouter
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
}));

// Mock toast
jest.mock("sonner", () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

const mockPush = jest.fn();
const mockSession = useSession as jest.MockedFunction<typeof useSession>;
const mockRouter = useRouter as jest.MockedFunction<typeof useRouter>;

describe("Profile Flow Integration", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockRouter.mockReturnValue({
      push: mockPush,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    } as any);

    mockSession.mockReturnValue({
      data: { user: mockUsers[0] },
    } as any);
  });

  describe("Complete Profile Update Flow", () => {
    it("should complete full profile update flow", async () => {
      const updatedUser = {
        ...mockUsers[0],
        name: "Updated Name",
        phone: "0999888777",
      };

      // Setup MSW handlers
      server.use(
        http.get("/api/profile", () => {
          return HttpResponse.json(mockUsers[0]);
        }),
        http.put("/api/profile", async ({ request }) => {
          const body = await request.json() as any;
          return HttpResponse.json({
            ...mockUsers[0],
            ...body,
          });
        })
      );

      const { toast } = require("sonner");

      render(<ProfilePage />);

      // Wait for profile to load
      await waitFor(() => {
        expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      });

      // Verify initial data is displayed
      expect(screen.getByText(mockUsers[0].name)).toBeInTheDocument();
      expect(screen.getByText(mockUsers[0].email)).toBeInTheDocument();

      // Enter edit mode
      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      // Update form fields
      const nameInput = screen.getByDisplayValue(mockUsers[0].name);
      const phoneInput = screen.getByDisplayValue(mockUsers[0].phone);

      fireEvent.change(nameInput, { target: { value: "Updated Name" } });
      fireEvent.change(phoneInput, { target: { value: "0999888777" } });

      // Submit form
      const saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);

      // Wait for success message
      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith("Cập nhật thông tin thành công");
      });

      // Verify form exits edit mode
      await waitFor(() => {
        expect(screen.getByText("Updated Name")).toBeInTheDocument();
        expect(screen.getByText("0999888777")).toBeInTheDocument();
        expect(screen.queryByDisplayValue("Updated Name")).not.toBeInTheDocument();
      });
    });

    it("should handle profile fetch error", async () => {
      server.use(
        http.get("/api/profile", () => {
          return HttpResponse.json(
            { error: "Failed to fetch profile" },
            { status: 500 }
          );
        })
      );

      const { toast } = require("sonner");

      render(<ProfilePage />);

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith("Có lỗi xảy ra khi tải thông tin cá nhân");
      });
    });

    it("should handle profile update error", async () => {
      server.use(
        http.get("/api/profile", () => {
          return HttpResponse.json(mockUsers[0]);
        }),
        http.put("/api/profile", () => {
          return HttpResponse.json(
            { error: "Họ tên là bắt buộc" },
            { status: 400 }
          );
        })
      );

      const { toast } = require("sonner");

      render(<ProfilePage />);

      // Wait for profile to load
      await waitFor(() => {
        expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      });

      // Enter edit mode
      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      // Clear name field
      const nameInput = screen.getByDisplayValue(mockUsers[0].name);
      fireEvent.change(nameInput, { target: { value: "" } });

      // Submit form
      const saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);

      // Wait for error message
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith("Họ tên là bắt buộc");
      });

      // Should remain in edit mode
      expect(screen.getByDisplayValue("")).toBeInTheDocument();
    });

    it("should handle network error during update", async () => {
      server.use(
        http.get("/api/profile", () => {
          return HttpResponse.json(mockUsers[0]);
        }),
        http.put("/api/profile", () => {
          return HttpResponse.error();
        })
      );

      const { toast } = require("sonner");

      render(<ProfilePage />);

      // Wait for profile to load
      await waitFor(() => {
        expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      });

      // Enter edit mode and make changes
      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      const nameInput = screen.getByDisplayValue(mockUsers[0].name);
      fireEvent.change(nameInput, { target: { value: "New Name" } });

      // Submit form
      const saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);

      // Wait for error message
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith("Có lỗi xảy ra khi cập nhật thông tin");
      });
    });
  });

  describe("Form Validation Flow", () => {
    beforeEach(() => {
      server.use(
        http.get("/api/profile", () => {
          return HttpResponse.json(mockUsers[0]);
        })
      );
    });

    it("should validate all form fields", async () => {
      render(<ProfilePage />);

      await waitFor(() => {
        expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      });

      // Enter edit mode
      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      // Test all form fields
      const nameInput = screen.getByDisplayValue(mockUsers[0].name);
      const phoneInput = screen.getByDisplayValue(mockUsers[0].phone);
      const dateInput = screen.getByDisplayValue("1990-05-15");
      const genderSelect = screen.getByDisplayValue("MALE");

      // Verify all fields are editable
      expect(nameInput).not.toBeDisabled();
      expect(phoneInput).not.toBeDisabled();
      expect(dateInput).not.toBeDisabled();
      expect(genderSelect).not.toBeDisabled();

      // Test field updates
      fireEvent.change(nameInput, { target: { value: "New Name" } });
      fireEvent.change(phoneInput, { target: { value: "0987654321" } });
      fireEvent.change(dateInput, { target: { value: "1995-12-25" } });
      fireEvent.change(genderSelect, { target: { value: "FEMALE" } });

      expect(nameInput).toHaveValue("New Name");
      expect(phoneInput).toHaveValue("0987654321");
      expect(dateInput).toHaveValue("1995-12-25");
      expect(genderSelect).toHaveValue("FEMALE");
    });

    it("should handle date field validation", async () => {
      server.use(
        http.put("/api/profile", async ({ request }) => {
          const body = await request.json() as any;
          
          // Simulate server-side date validation
          if (body.dateOfBirth && new Date(body.dateOfBirth).getFullYear() > 2010) {
            return HttpResponse.json(
              { error: "Tuổi phải từ 13 đến 120" },
              { status: 400 }
            );
          }
          
          return HttpResponse.json({
            ...mockUsers[0],
            ...body,
          });
        })
      );

      const { toast } = require("sonner");

      render(<ProfilePage />);

      await waitFor(() => {
        expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      });

      // Enter edit mode
      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      // Set invalid date (too young)
      const dateInput = screen.getByDisplayValue("1990-05-15");
      fireEvent.change(dateInput, { target: { value: "2015-01-01" } });

      // Submit form
      const saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);

      // Wait for validation error
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith("Tuổi phải từ 13 đến 120");
      });
    });

    it("should handle phone number validation", async () => {
      server.use(
        http.put("/api/profile", async ({ request }) => {
          const body = await request.json() as any;
          
          // Simulate server-side phone validation
          if (body.phone && !/^[0-9]{10,11}$/.test(body.phone)) {
            return HttpResponse.json(
              { error: "Số điện thoại không hợp lệ" },
              { status: 400 }
            );
          }
          
          return HttpResponse.json({
            ...mockUsers[0],
            ...body,
          });
        })
      );

      const { toast } = require("sonner");

      render(<ProfilePage />);

      await waitFor(() => {
        expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      });

      // Enter edit mode
      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      // Set invalid phone
      const phoneInput = screen.getByDisplayValue(mockUsers[0].phone);
      fireEvent.change(phoneInput, { target: { value: "invalid-phone" } });

      // Submit form
      const saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);

      // Wait for validation error
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith("Số điện thoại không hợp lệ");
      });
    });
  });

  describe("Cancel and Reset Flow", () => {
    beforeEach(() => {
      server.use(
        http.get("/api/profile", () => {
          return HttpResponse.json(mockUsers[0]);
        })
      );
    });

    it("should cancel changes and restore original values", async () => {
      render(<ProfilePage />);

      await waitFor(() => {
        expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      });

      // Enter edit mode
      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      // Make changes
      const nameInput = screen.getByDisplayValue(mockUsers[0].name);
      const phoneInput = screen.getByDisplayValue(mockUsers[0].phone);

      fireEvent.change(nameInput, { target: { value: "Changed Name" } });
      fireEvent.change(phoneInput, { target: { value: "0999999999" } });

      // Verify changes
      expect(nameInput).toHaveValue("Changed Name");
      expect(phoneInput).toHaveValue("0999999999");

      // Cancel changes
      const cancelButton = screen.getByText("Hủy");
      fireEvent.click(cancelButton);

      // Verify original values are restored and edit mode is exited
      await waitFor(() => {
        expect(screen.getByText(mockUsers[0].name)).toBeInTheDocument();
        expect(screen.getByText(mockUsers[0].phone)).toBeInTheDocument();
        expect(screen.queryByDisplayValue("Changed Name")).not.toBeInTheDocument();
      });
    });

    it("should handle multiple edit sessions", async () => {
      server.use(
        http.put("/api/profile", async ({ request }) => {
          const body = await request.json() as any;
          return HttpResponse.json({
            ...mockUsers[0],
            ...body,
          });
        })
      );

      const { toast } = require("sonner");

      render(<ProfilePage />);

      await waitFor(() => {
        expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      });

      // First edit session
      let editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      let nameInput = screen.getByDisplayValue(mockUsers[0].name);
      fireEvent.change(nameInput, { target: { value: "First Update" } });

      let saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith("Cập nhật thông tin thành công");
      });

      // Second edit session
      await waitFor(() => {
        editButton = screen.getByText("Chỉnh sửa");
        fireEvent.click(editButton);
      });

      nameInput = screen.getByDisplayValue("First Update");
      fireEvent.change(nameInput, { target: { value: "Second Update" } });

      saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledTimes(2);
        expect(screen.getByText("Second Update")).toBeInTheDocument();
      });
    });
  });

  describe("Authentication Flow", () => {
    it("should redirect to signin when not authenticated", () => {
      mockSession.mockReturnValue({ data: null } as any);

      render(<ProfilePage />);

      expect(mockPush).toHaveBeenCalledWith("/auth/signin");
    });

    it("should handle session expiry during profile update", async () => {
      server.use(
        http.get("/api/profile", () => {
          return HttpResponse.json(mockUsers[0]);
        }),
        http.put("/api/profile", () => {
          return HttpResponse.json(
            { error: "Vui lòng đăng nhập" },
            { status: 401 }
          );
        })
      );

      const { toast } = require("sonner");

      render(<ProfilePage />);

      await waitFor(() => {
        expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      });

      // Enter edit mode and make changes
      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      const nameInput = screen.getByDisplayValue(mockUsers[0].name);
      fireEvent.change(nameInput, { target: { value: "New Name" } });

      // Submit form
      const saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);

      // Should show authentication error
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith("Vui lòng đăng nhập");
      });
    });
  });

  describe("Loading States", () => {
    it("should show loading state during profile fetch", async () => {
      server.use(
        http.get("/api/profile", async () => {
          await new Promise(resolve => setTimeout(resolve, 100));
          return HttpResponse.json(mockUsers[0]);
        })
      );

      render(<ProfilePage />);

      // Should show loading initially
      expect(screen.getByText("Đang tải...")).toBeInTheDocument();

      // Wait for profile to load
      await waitFor(() => {
        expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      }, { timeout: 2000 });

      expect(screen.queryByText("Đang tải...")).not.toBeInTheDocument();
    });

    it("should disable form during update", async () => {
      server.use(
        http.get("/api/profile", () => {
          return HttpResponse.json(mockUsers[0]);
        }),
        http.put("/api/profile", async () => {
          await new Promise(resolve => setTimeout(resolve, 100));
          return HttpResponse.json(mockUsers[0]);
        })
      );

      render(<ProfilePage />);

      await waitFor(() => {
        expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      });

      // Enter edit mode
      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      // Submit form
      const saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);

      // Form should be disabled during update
      // (This would depend on actual implementation)
      await waitFor(() => {
        expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      }, { timeout: 2000 });
    });
  });
});
