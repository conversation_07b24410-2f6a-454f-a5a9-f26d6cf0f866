/**
 * Profile Flow Integration Tests (Simplified)
 * Kiểm tra integration cho toàn bộ flow update profile từ form đến API
 */

import React from "react";
import { render, screen, fireEvent, waitFor } from "../helpers/test-utils";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { mockUsers, mockApiResponses } from "../fixtures/mock-data";

// Mock useSession
jest.mock("next-auth/react", () => ({
  useSession: jest.fn(),
}));

// Mock useRouter
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
}));

// Mock toast
jest.mock("sonner", () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock fetch
global.fetch = jest.fn();

const mockPush = jest.fn();
const mockSession = useSession as jest.MockedFunction<typeof useSession>;
const mockRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

// Simple Profile Integration Component
const ProfileIntegration = () => {
  const [user, setUser] = React.useState<any>(null);
  const [loading, setLoading] = React.useState(true);
  const [isEditing, setIsEditing] = React.useState(false);
  const [formData, setFormData] = React.useState({
    name: "",
    phone: "",
    dateOfBirth: "",
    gender: "",
  });

  React.useEffect(() => {
    const fetchProfile = async () => {
      try {
        const response = await fetch("/api/profile");
        if (response.ok) {
          const userData = await response.json();
          setUser(userData);
          setFormData({
            name: userData.name || "",
            phone: userData.phone || "",
            dateOfBirth: userData.dateOfBirth ? new Date(userData.dateOfBirth).toISOString().split('T')[0] : "",
            gender: userData.gender || "",
          });
        } else {
          throw new Error("Failed to fetch profile");
        }
      } catch (error) {
        console.error("Profile fetch error:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, []);

  const handleUpdate = async (data: any) => {
    try {
      const response = await fetch("/api/profile", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const updatedUser = await response.json();
        setUser(updatedUser);
        setIsEditing(false);
        // Simulate toast success
        const { toast } = require("sonner");
        toast.success("Cập nhật thông tin thành công");
      } else {
        const error = await response.json();
        throw new Error(error.error || "Update failed");
      }
    } catch (error) {
      const { toast } = require("sonner");
      toast.error(error instanceof Error ? error.message : "Có lỗi xảy ra khi cập nhật thông tin");
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleUpdate(formData);
  };

  const handleCancel = () => {
    setFormData({
      name: user?.name || "",
      phone: user?.phone || "",
      dateOfBirth: user?.dateOfBirth ? new Date(user.dateOfBirth).toISOString().split('T')[0] : "",
      gender: user?.gender || "",
    });
    setIsEditing(false);
  };

  if (loading) {
    return <div>Đang tải...</div>;
  }

  if (!user) {
    return <div>Không tìm thấy thông tin người dùng</div>;
  }

  if (!isEditing) {
    return (
      <div>
        <h1>Thông tin cá nhân</h1>
        <div>
          <p>Họ tên: {user.name}</p>
          <p>Email: {user.email}</p>
          <p>Số điện thoại: {user.phone}</p>
          <p>Ngày sinh: {user.dateOfBirth ? new Date(user.dateOfBirth).toLocaleDateString("vi-VN") : "Chưa cập nhật"}</p>
          <p>Giới tính: {user.gender === "MALE" ? "Nam" : user.gender === "FEMALE" ? "Nữ" : user.gender === "OTHER" ? "Khác" : "Chưa cập nhật"}</p>
        </div>
        <button onClick={() => setIsEditing(true)}>Chỉnh sửa</button>
      </div>
    );
  }

  return (
    <div>
      <h1>Chỉnh sửa thông tin</h1>
      <form onSubmit={handleSubmit}>
        <div>
          <label>Họ tên:</label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          />
        </div>
        <div>
          <label>Số điện thoại:</label>
          <input
            type="text"
            value={formData.phone}
            onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
          />
        </div>
        <div>
          <label>Ngày sinh:</label>
          <input
            type="date"
            value={formData.dateOfBirth}
            onChange={(e) => setFormData({ ...formData, dateOfBirth: e.target.value })}
          />
        </div>
        <div>
          <label>Giới tính:</label>
          <select
            value={formData.gender}
            onChange={(e) => setFormData({ ...formData, gender: e.target.value })}
          >
            <option value="">Chọn giới tính</option>
            <option value="MALE">Nam</option>
            <option value="FEMALE">Nữ</option>
            <option value="OTHER">Khác</option>
          </select>
        </div>
        <button type="submit">Lưu thay đổi</button>
        <button type="button" onClick={handleCancel}>Hủy</button>
      </form>
    </div>
  );
};

describe("Profile Flow Integration (Simplified)", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockRouter.mockReturnValue({
      push: mockPush,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    } as any);

    mockSession.mockReturnValue({
      data: { user: mockUsers[0] },
    } as any);
  });

  describe("Complete Profile Update Flow", () => {
    it("should complete full profile update flow", async () => {
      const updatedUser = {
        ...mockUsers[0],
        name: "Updated Name",
        phone: "0999888777",
      };

      // Mock fetch responses
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockUsers[0],
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => updatedUser,
        } as Response);

      const { toast } = require("sonner");

      render(<ProfileIntegration />);

      // Wait for profile to load
      await waitFor(() => {
        expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      });

      // Verify initial data is displayed
      expect(screen.getByText(`Họ tên: ${mockUsers[0].name}`)).toBeInTheDocument();
      expect(screen.getByText(`Email: ${mockUsers[0].email}`)).toBeInTheDocument();

      // Enter edit mode
      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      // Update form fields
      const nameInput = screen.getByDisplayValue(mockUsers[0].name);
      const phoneInput = screen.getByDisplayValue(mockUsers[0].phone);

      fireEvent.change(nameInput, { target: { value: "Updated Name" } });
      fireEvent.change(phoneInput, { target: { value: "0999888777" } });

      // Submit form
      const saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);

      // Wait for success message
      await waitFor(() => {
        expect(toast.success).toHaveBeenCalledWith("Cập nhật thông tin thành công");
      });

      // Verify form exits edit mode
      await waitFor(() => {
        expect(screen.getByText("Họ tên: Updated Name")).toBeInTheDocument();
        expect(screen.getByText("Số điện thoại: 0999888777")).toBeInTheDocument();
      });
    });

    it("should handle profile fetch error", async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        json: async () => ({ error: "Failed to fetch profile" }),
      } as Response);

      render(<ProfileIntegration />);

      await waitFor(() => {
        expect(screen.getByText("Không tìm thấy thông tin người dùng")).toBeInTheDocument();
      });
    });

    it("should handle profile update error", async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockUsers[0],
        } as Response)
        .mockResolvedValueOnce({
          ok: false,
          json: async () => ({ error: "Họ tên là bắt buộc" }),
        } as Response);

      const { toast } = require("sonner");

      render(<ProfileIntegration />);

      // Wait for profile to load
      await waitFor(() => {
        expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      });

      // Enter edit mode
      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      // Clear name field
      const nameInput = screen.getByDisplayValue(mockUsers[0].name);
      fireEvent.change(nameInput, { target: { value: "" } });

      // Submit form
      const saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);

      // Wait for error message
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith("Họ tên là bắt buộc");
      });

      // Should remain in edit mode
      expect(screen.getByDisplayValue("")).toBeInTheDocument();
    });

    it("should handle network error during update", async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockUsers[0],
        } as Response)
        .mockRejectedValueOnce(new Error("Network error"));

      const { toast } = require("sonner");

      render(<ProfileIntegration />);

      // Wait for profile to load
      await waitFor(() => {
        expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      });

      // Enter edit mode and make changes
      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      const nameInput = screen.getByDisplayValue(mockUsers[0].name);
      fireEvent.change(nameInput, { target: { value: "New Name" } });

      // Submit form
      const saveButton = screen.getByText("Lưu thay đổi");
      fireEvent.click(saveButton);

      // Wait for error message
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith("Có lỗi xảy ra khi cập nhật thông tin");
      });
    });
  });

  describe("Cancel and Reset Flow", () => {
    it("should cancel changes and restore original values", async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => mockUsers[0],
      } as Response);

      render(<ProfileIntegration />);

      await waitFor(() => {
        expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      });

      // Enter edit mode
      const editButton = screen.getByText("Chỉnh sửa");
      fireEvent.click(editButton);

      // Make changes
      const nameInput = screen.getByDisplayValue(mockUsers[0].name);
      const phoneInput = screen.getByDisplayValue(mockUsers[0].phone);

      fireEvent.change(nameInput, { target: { value: "Changed Name" } });
      fireEvent.change(phoneInput, { target: { value: "0999999999" } });

      // Verify changes
      expect(nameInput).toHaveValue("Changed Name");
      expect(phoneInput).toHaveValue("0999999999");

      // Cancel changes
      const cancelButton = screen.getByText("Hủy");
      fireEvent.click(cancelButton);

      // Verify original values are restored and edit mode is exited
      await waitFor(() => {
        expect(screen.getByText(`Họ tên: ${mockUsers[0].name}`)).toBeInTheDocument();
        expect(screen.getByText(`Số điện thoại: ${mockUsers[0].phone}`)).toBeInTheDocument();
      });
    });
  });

  describe("Loading States", () => {
    it("should show loading state during profile fetch", async () => {
      mockFetch.mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({
            ok: true,
            json: async () => mockUsers[0],
          } as Response), 100)
        )
      );

      render(<ProfileIntegration />);

      // Should show loading initially
      expect(screen.getByText("Đang tải...")).toBeInTheDocument();

      // Wait for profile to load
      await waitFor(() => {
        expect(screen.getByText("Thông tin cá nhân")).toBeInTheDocument();
      }, { timeout: 2000 });

      expect(screen.queryByText("Đang tải...")).not.toBeInTheDocument();
    });
  });
});
