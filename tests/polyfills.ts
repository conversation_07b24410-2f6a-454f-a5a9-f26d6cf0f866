/**
 * Polyfills for testing environment
 * Các polyfill cần thiết cho môi trường testing
 */

import { TextEncoder, TextDecoder } from 'util'

// Polyfill TextEncoder/TextDecoder for Node.js
global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder as any

// Polyfill fetch for Node.js
import 'whatwg-fetch'

// Polyfill URL for Node.js if needed
if (typeof global.URL === 'undefined') {
  global.URL = require('url').URL
}

// Polyfill URLSearchParams for Node.js if needed
if (typeof global.URLSearchParams === 'undefined') {
  global.URLSearchParams = require('url').URLSearchParams
}

// Polyfill crypto for Node.js if needed
if (typeof global.crypto === 'undefined') {
  const { webcrypto } = require('crypto')
  global.crypto = webcrypto
}

// Polyfill performance for Node.js if needed
if (typeof global.performance === 'undefined') {
  const { performance } = require('perf_hooks')
  global.performance = performance
}
